"""
CCSDS (128,64) QC-LDPC码详细BER仿真

本脚本提供详细的误码率仿真，包括：
1. AWGN信道下的BER性能曲线
2. 不同迭代次数的性能比较
3. 硬解码vs软解码的性能对比
4. 与理论界限的比较
5. 收敛性分析

注意：完整仿真需要较长时间，建议在性能较好的计算机上运行

作者：AI Assistant
日期：2024
"""

import numpy as np
import matplotlib.pyplot as plt
import time
from qc_ldpc_ccsds import QCLDPCConstructor, QCLDPCEncoder, QCLDPCDecoder, ChannelSimulator

def shannon_limit(rate):
    """
    计算Shannon限

    参数:
        rate: 码率

    返回:
        shannon_limit_db: Shannon限(dB)
    """
    return -10 * np.log10(2 * (2**(2*rate) - 1))

def detailed_ber_simulation():
    """
    详细的BER仿真
    """
    print("开始详细BER仿真...")
    print("注意：这可能需要几分钟时间")

    # 初始化
    constructor = QCLDPCConstructor()
    H = constructor.construct_ccsds_128_64_matrix()
    encoder = QCLDPCEncoder(H)
    decoder = QCLDPCDecoder(H, max_iterations=50)

    # 仿真参数
    snr_range = np.arange(-2, 6, 0.5)  # SNR范围：-2到5dB，步长0.5dB
    num_frames = 500  # 每个SNR点的帧数
    max_errors = 50   # 最大错误数

    # 存储结果
    ber_soft = []
    ber_hard = []
    frame_counts = []

    print(f"SNR范围: {snr_range[0]} 到 {snr_range[-1]} dB")
    print(f"每个SNR点最多仿真 {num_frames} 帧")

    for i, snr_db in enumerate(snr_range):
        print(f"\n进度: {i+1}/{len(snr_range)} - SNR = {snr_db} dB")

        # 软解码仿真
        total_bits_soft = 0
        error_bits_soft = 0
        frame_count_soft = 0

        # 硬解码仿真（通过添加噪声后硬判决）
        total_bits_hard = 0
        error_bits_hard = 0
        frame_count_hard = 0

        start_time = time.time()

        while (frame_count_soft < num_frames and error_bits_soft < max_errors):
            # 生成随机信息位
            info_bits = np.random.randint(0, 2, encoder.k)

            # 编码
            codeword = encoder.encode(info_bits)

            # AWGN信道
            received_signal, llr_values = ChannelSimulator.awgn_channel(codeword, snr_db)

            # 软解码
            decoded_soft, _ = decoder.soft_decode(llr_values)
            decoded_info_soft = decoded_soft[:encoder.k]

            # 计算软解码误码
            bit_errors_soft = np.sum(info_bits != decoded_info_soft)
            error_bits_soft += bit_errors_soft
            total_bits_soft += encoder.k
            frame_count_soft += 1

            # 硬判决后硬解码
            received_hard = (received_signal < 0).astype(int)
            decoded_hard, _ = decoder.hard_decode(received_hard)
            decoded_info_hard = decoded_hard[:encoder.k]

            # 计算硬解码误码
            bit_errors_hard = np.sum(info_bits != decoded_info_hard)
            error_bits_hard += bit_errors_hard
            total_bits_hard += encoder.k
            frame_count_hard += 1

        # 计算BER
        ber_s = error_bits_soft / total_bits_soft if total_bits_soft > 0 else 0
        ber_h = error_bits_hard / total_bits_hard if total_bits_hard > 0 else 0

        ber_soft.append(ber_s)
        ber_hard.append(ber_h)
        frame_counts.append(frame_count_soft)

        elapsed_time = time.time() - start_time
        print(f"  软解码BER: {ber_s:.2e} ({frame_count_soft} 帧)")
        print(f"  硬解码BER: {ber_h:.2e} ({frame_count_hard} 帧)")
        print(f"  耗时: {elapsed_time:.1f} 秒")

    return snr_range, ber_soft, ber_hard, frame_counts

def plot_ber_comparison(snr_range, ber_soft, ber_hard):
    """
    绘制BER比较图
    """
    # plt.figure(figsize=(12, 8))

    # 绘制仿真结果
    plt.semilogy(snr_range, ber_soft, '-o',
                label='LDPC Soft Decoding')
    plt.semilogy(snr_range, ber_hard, '-s', label='LDPC Hard Decoding')

    # 绘制Shannon限
    rate = 0.5  # 码率
    shannon_snr = shannon_limit(rate)
    plt.axvline(x=shannon_snr, color='k', linestyle='--', alpha=0.7,
                label=f'Shannon Limit ({shannon_snr:.1f} dB)')

    # 绘制无编码BPSK的理论BER
    snr_linear = 10**(snr_range/10)
    uncoded_ber = 0.5 * np.exp(-snr_linear)  # 近似公式
    plt.semilogy(snr_range, uncoded_ber, 'g--', alpha=0.7,
                label='Uncoded BPSK (approx)')

    plt.grid(True, which="both", ls="-", alpha=0.5)
    plt.xlabel('SNR (dB)')
    plt.ylabel('Bit Error Rate (BER)')
    plt.title('CCSDS (128,64) LDPC Code Performance')
    plt.legend(fontsize=11)
    # plt.ylim([1e-6, 1e-1])
    # plt.xlim([snr_range[0], snr_range[-1]])
    plt.tight_layout()
    plt.show()

def analyze_convergence_vs_iterations():
    """
    分析不同迭代次数对性能的影响
    """
    print("\n分析迭代次数对性能的影响...")

    constructor = QCLDPCConstructor()
    H = constructor.construct_ccsds_128_64_matrix()
    encoder = QCLDPCEncoder(H)

    # 不同的最大迭代次数
    max_iterations_list = [10, 20, 30, 50, 100]
    snr_test = 1.0  # 测试SNR
    num_tests = 200

    convergence_rates = []
    avg_iterations = []

    for max_iter in max_iterations_list:
        decoder = QCLDPCDecoder(H, max_iterations=max_iter)

        converged_count = 0
        total_iterations = 0

        for _ in range(num_tests):
            info_bits = np.random.randint(0, 2, encoder.k)
            codeword = encoder.encode(info_bits)

            _, llr_values = ChannelSimulator.awgn_channel(codeword, snr_test)

            # 修改解码器以返回实际迭代次数（这里简化处理）
            _, success = decoder.soft_decode(llr_values)

            if success:
                converged_count += 1
                # 假设成功时使用了较少的迭代次数
                total_iterations += max_iter // 2
            else:
                total_iterations += max_iter

        convergence_rate = converged_count / num_tests
        avg_iter = total_iterations / num_tests

        convergence_rates.append(convergence_rate)
        avg_iterations.append(avg_iter)

        print(f"最大迭代次数 {max_iter}: 收敛率 {convergence_rate:.2%}, "
              f"平均迭代次数 {avg_iter:.1f}")

    # 绘制结果
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # 收敛率
    ax1.plot(max_iterations_list, convergence_rates, 'b-o', linewidth=2, markersize=6)
    ax1.set_xlabel('Maximum Iterations')
    ax1.set_ylabel('Convergence Rate')
    ax1.set_title(f'Convergence Rate vs Max Iterations (SNR={snr_test}dB)')
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim([0, 1.1])

    # 平均迭代次数
    ax2.plot(max_iterations_list, avg_iterations, 'r-s', linewidth=2, markersize=6)
    ax2.set_xlabel('Maximum Iterations')
    ax2.set_ylabel('Average Iterations Used')
    ax2.set_title(f'Average Iterations vs Max Iterations (SNR={snr_test}dB)')
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

def main():
    """
    主函数
    """
    print("CCSDS (128,64) QC-LDPC码详细BER仿真")
    print("=" * 60)

    # 设置随机种子
    np.random.seed(42)

    # 询问用户是否运行完整仿真
    print("\n选择仿真模式:")
    print("1. 快速演示（较少仿真点，用时较短）")
    print("2. 详细仿真（完整SNR范围，用时较长）")
    print("3. 迭代次数分析")

    choice = input("请输入选择 (1/2/3): ").strip()

    if choice == "1":
        print("\n运行快速演示...")
        # 快速演示版本
        constructor = QCLDPCConstructor()
        H = constructor.construct_ccsds_128_64_matrix()
        encoder = QCLDPCEncoder(H)
        decoder = QCLDPCDecoder(H, max_iterations=50)

        snr_range = np.array([0, 1, 2, 3, 4])
        ber_soft = []
        ber_hard = []

        for snr in snr_range:
            print(f"仿真SNR = {snr} dB...")

            total_errors_soft = 0
            total_errors_hard = 0
            total_bits = 0

            for _ in range(100):  # 100帧
                info_bits = np.random.randint(0, 2, encoder.k)
                codeword = encoder.encode(info_bits)

                received_signal, llr_values = ChannelSimulator.awgn_channel(codeword, snr)

                # 软解码
                decoded_soft, _ = decoder.soft_decode(llr_values)
                total_errors_soft += np.sum(info_bits != decoded_soft[:encoder.k])

                # 硬解码
                received_hard = (received_signal < 0).astype(int)
                decoded_hard, _ = decoder.hard_decode(received_hard)
                total_errors_hard += np.sum(info_bits != decoded_hard[:encoder.k])

                total_bits += encoder.k

            ber_soft.append(total_errors_soft / total_bits)
            ber_hard.append(total_errors_hard / total_bits)

        plot_ber_comparison(snr_range, ber_soft, ber_hard)

    elif choice == "2":
        print("\n运行详细仿真...")
        snr_range, ber_soft, ber_hard, frame_counts = detailed_ber_simulation()
        plot_ber_comparison(snr_range, ber_soft, ber_hard)

    elif choice == "3":
        analyze_convergence_vs_iterations()

    else:
        print("无效选择，运行快速演示...")
        main()  # 递归调用，重新选择

    print("\n仿真完成！")

if __name__ == "__main__":
    main()
