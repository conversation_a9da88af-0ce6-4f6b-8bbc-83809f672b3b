# LDPC和SC-LDPC编解码器

本目录包含了完整的LDPC (Low-Density Parity-Check) 和SC-LDPC (Spatially Coupled LDPC) 编解码器实现，支持编码、解码和性能仿真。

## 文件说明

### 核心文件
- `ldpc.py` - 主要的LDPC和SC-LDPC编解码器实现
- `ldpc_demo.py` - LDPC演示脚本，展示各种功能
- `ldpc_test.py` - LDPC测试脚本，用于验证功能
- `sc_ldpc_demo.py` - SC-LDPC演示脚本
- `sc_ldpc_test.py` - SC-LDPC测试脚本
- `debug_ldpc.py` - 调试脚本，用于问题诊断
- `README.md` - 本说明文档

## 主要功能

### 1. 传统LDPC编解码器

#### LDPC编码器 (LDPCEncoder)
- 支持系统码和非系统码
- 基于校验矩阵的编码
- 自动生成生成矩阵

#### LDPC解码器 (LDPCDecoder)
- 基于置信传播算法的软判决解码
- 支持迭代解码和收敛检测
- 校验子检查功能

#### 校验矩阵生成
- 规则LDPC码生成
- 不规则LDPC码生成
- 手工构造的示例码

### 2. SC-LDPC编解码器

#### SC-LDPC构造器 (SCLDPCConstructor)
- 基于原型矩阵的空间耦合构造
- 支持不同的耦合长度和记忆长度
- 边界处理（终止和尾比特）

#### SC-LDPC编码器 (SCLDPCEncoder)
- 基于耦合结构的系统编码
- 支持流式编码
- 自适应参数处理

#### SC-LDPC解码器 (SCLDPCDecoder)
- **硬解码**：基于置信传播的硬判决解码
- **软解码**：基于置信传播的软判决解码
- **滑窗解码**：适用于长SC-LDPC码的滑窗解码

### 3. 性能仿真
- AWGN信道模拟
- BER/FER性能评估
- 多种解码方法比较
- 可视化性能曲线

## 快速开始

### 传统LDPC使用

```python
from channel_coding.ldpc import *
import numpy as np

# 创建校验矩阵
H = np.array([
    [1, 1, 0, 1, 0, 0],
    [0, 1, 1, 0, 1, 0],
    [1, 0, 1, 0, 0, 1]
])

# 创建编解码器
encoder = LDPCEncoder(H)
decoder = LDPCDecoder(H)

# 编码
info_bits = np.array([1, 0, 1])
codeword = encoder.encode(info_bits)
print(f"信息位: {info_bits}")
print(f"码字: {codeword}")

# 解码
symbols = bits_to_bpsk(codeword)
llr = bpsk_to_llr(symbols, 0.01)
decoded_bits, converged, iterations = decoder.decode(llr)
decoded_info = decoded_bits[:len(info_bits)]
print(f"解码信息位: {decoded_info}")
```

### SC-LDPC使用

```python
from channel_coding.ldpc import *
import numpy as np

# 创建SC-LDPC码
base_matrix = np.array([
    [1, 1, 0],
    [0, 1, 1]
])

# 构造空间耦合矩阵
H_coupled = generate_sc_ldpc_matrix(
    base_matrix,
    coupling_length=5,
    memory_length=2
)

# 创建编解码器
encoder = SCLDPCEncoder(H_coupled)
decoder = SCLDPCDecoder(H_coupled)

# 编码
params = encoder.get_code_parameters()
info_bits = np.random.randint(0, 2, params['k'])
codeword = encoder.encode(info_bits)

# 软解码
symbols = bits_to_bpsk(codeword)
received_signal, noise_var = awgn_channel(symbols, snr_db=2)
llr = bpsk_to_llr(received_signal, noise_var)
decoded_bits, converged, iterations = decoder.decode_soft(llr)

# 硬解码
hard_bits = (received_signal < 0).astype(int)
decoded_hard, converged_hard, iter_hard = decoder.decode_hard(hard_bits)

# 滑窗解码（适用于长码）
if len(codeword) > 20:
    window_size = len(codeword) // 2
    decoded_sliding, avg_iter = decoder.decode_sliding_window(
        llr, window_size, overlap=5
    )
```

### 性能仿真

```python
# 传统LDPC性能仿真
examples = create_example_ldpc_codes()
H = examples['manual_6_3']['H']

snr_range = range(0, 6)
snr_list, ber_list, fer_list = ldpc_simulation(
    H, snr_range, num_frames=100, verbose=True
)

# SC-LDPC性能仿真
examples_sc = create_example_sc_ldpc_codes()
H_coupled = examples_sc['sc_ldpc_5_2']['H']

# 比较不同解码方法
methods = ['soft', 'hard', 'sliding_window']
for method in methods:
    snr_list, ber_list, fer_list = sc_ldpc_simulation(
        H_coupled, [0, 2, 4], num_frames=50,
        decode_method=method, verbose=True
    )

# 绘制性能曲线
plot_performance(snr_list, ber_list, fer_list)
```

## 运行演示和测试

### 运行演示程序

```bash
# 传统LDPC演示
python ldpc_demo.py

# SC-LDPC演示
python sc_ldpc_demo.py
```

### 运行测试程序

```bash
# 传统LDPC测试
python ldpc_test.py

# SC-LDPC测试
python sc_ldpc_test.py
```

## SC-LDPC码的特点

### 1. 空间耦合结构
- **耦合长度 (L)**：决定了码的长度和性能
- **记忆长度 (m_s)**：控制节点间的连接范围
- **边界效应**：在码的边界处具有特殊的解码性能

### 2. 解码方法比较

| 解码方法 | 复杂度 | 性能 | 适用场景 |
|---------|--------|------|----------|
| 硬解码 | 低 | 中等 | 资源受限环境 |
| 软解码 | 中等 | 高 | 一般应用 |
| 滑窗解码 | 中等 | 高 | 长码字、流式处理 |

### 3. 性能优势
- **阈值改善**：相比传统LDPC码有更好的阈值性能
- **有限长度性能**：在有限长度下接近容量极限
- **低复杂度**：滑窗解码可以降低解码复杂度

## 技术细节

### SC-LDPC构造原理

SC-LDPC码通过将基础LDPC码在空间上进行耦合构造：

1. **基础矩阵**：定义基本的连接模式
2. **空间耦合**：将基础矩阵在不同位置重复放置
3. **边界处理**：处理码的起始和结束部分

### 解码算法

#### 置信传播算法
- **变量节点更新**：基于相邻校验节点的消息
- **校验节点更新**：基于相邻变量节点的消息
- **收敛判断**：基于消息变化的阈值

#### 滑窗解码
- **窗口划分**：将长码字分成重叠的窗口
- **局部解码**：在每个窗口内独立解码
- **边界处理**：处理窗口间的重叠区域

## 参数建议

### 传统LDPC码
- **小码长**：n < 100，适合测试和学习
- **中等码长**：100 ≤ n ≤ 1000，适合一般应用
- **大码长**：n > 1000，适合高性能要求

### SC-LDPC码
- **耦合长度**：L = 10-50，平衡性能和复杂度
- **记忆长度**：m_s = 1-3，通常取1或2
- **窗口大小**：W = L/2 到 L，用于滑窗解码

## 常见问题

### Q1: 为什么SC-LDPC码的性能比传统LDPC码好？
A: SC-LDPC码通过空间耦合结构，在码的边界处产生了不规则的度分布，这种不规则性改善了解码阈值，使得码的性能更接近香农极限。

### Q2: 什么时候使用滑窗解码？
A: 当码长很长（通常 > 1000）或者需要流式处理时，滑窗解码可以显著降低内存需求和解码延迟。

### Q3: 如何选择合适的参数？
A:
- 对于高码率应用，选择较大的耦合长度
- 对于低延迟要求，选择较小的记忆长度
- 对于高可靠性要求，使用软解码

### Q4: 编码复杂度如何？
A: SC-LDPC的编码复杂度与传统LDPC相当，主要取决于校验矩阵的稀疏性。对于长码，可以使用流式编码来降低内存需求。

## 参考文献

1. Gallager, R. G. (1962). Low-density parity-check codes.
2. Kudekar, S., Richardson, T., & Urbanke, R. (2013). Threshold saturation via spatial coupling.
3. Lentmaier, M., & Zigangirov, K. S. (1999). On generalized low-density parity-check codes based on Hamming component codes.

## 许可证

本实现仅供学习和研究使用。

### 生成LDPC码

```python
# 生成规则LDPC码
H_regular = generate_regular_ldpc_matrix(n=15, k=11, dv=3, dc=11)

# 生成不规则LDPC码
var_dist = {2: 10, 3: 20}  # 变量节点度数分布
check_dist = {5: 12}       # 校验节点度数分布
H_irregular = generate_irregular_ldpc_matrix(
    n=30, k=18,
    var_degree_dist=var_dist,
    check_degree_dist=check_dist
)
```

## 运行演示

```bash
# 基本测试
python ldpc.py

# 完整演示
python ldpc_demo.py

# 调试测试
python debug_ldpc.py
```

## 技术细节

### BPSK调制约定
- 比特0 → BPSK符号-1
- 比特1 → BPSK符号+1

### LLR约定
- LLR > 0 表示比特更可能是0
- LLR < 0 表示比特更可能是1

### 置信传播算法
- 变量节点更新：消息传递
- 校验节点更新：双曲正切函数
- 迭代直到收敛或达到最大迭代次数

## 性能特点

### 优点
- 接近香农极限的性能
- 并行解码能力
- 灵活的码率设计

### 适用场景
- 数字通信系统
- 存储系统
- 卫星通信
- 无线通信

## 参数说明

### 编码器参数
- `H`: 校验矩阵 (m×n)
- `systematic`: 是否使用系统编码

### 解码器参数
- `H`: 校验矩阵
- `max_iterations`: 最大迭代次数
- `convergence_threshold`: 收敛阈值

### 仿真参数
- `snr_range`: 信噪比范围 (dB)
- `num_frames`: 仿真帧数
- `frame_length`: 每帧长度

## 注意事项

1. 校验矩阵必须满足LDPC码的要求（低密度）
2. 对于大型LDPC码，解码可能需要较长时间
3. 性能与校验矩阵的设计密切相关
4. 建议使用较大的码长以获得更好的性能

## 扩展功能

可以进一步扩展的功能：
- 更多的校验矩阵构造算法
- 其他调制方式支持
- 更高效的解码算法
- GPU加速实现
- 更多的性能评估指标

## 参考资料

- Gallager, R. G. "Low-density parity-check codes"
- MacKay, D. J. C. "Information Theory, Inference, and Learning Algorithms"
- Richardson, T. J. and Urbanke, R. L. "Modern Coding Theory"
