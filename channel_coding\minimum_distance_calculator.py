"""
CCSDS (128,64) QC-LDPC码最小汉明距离计算器

本脚本实现了多种方法来计算线性码的最小汉明距离：
1. 暴力搜索法（适用于小码长）
2. 基于校验矩阵的搜索法
3. 基于生成矩阵的搜索法
4. 启发式搜索法（适用于大码长）

最小汉明距离定义：
d_min = min{wt(c) : c ∈ C, c ≠ 0}
其中 wt(c) 是码字c的汉明重量，C是码字集合

作者：AI Assistant
日期：2024
"""

import numpy as np
import itertools
import math
from qc_ldpc_ccsds import QCLDPCConstructor, QCLDPCEncoder
import time

class MinimumDistanceCalculator:
    """
    最小汉明距离计算器
    """

    def __init__(self, H, G=None):
        """
        初始化计算器

        参数:
            H: 校验矩阵
            G: 生成矩阵（可选）
        """
        self.H = np.array(H, dtype=int)
        self.m, self.n = self.H.shape
        self.k = self.n - self.m
        self.G = G

        print(f"码参数: n={self.n}, k={self.k}, m={self.m}")
        print(f"理论码字数量: 2^{self.k} = {2**self.k}")

    def hamming_weight(self, vector):
        """
        计算向量的汉明重量

        参数:
            vector: 输入向量

        返回:
            weight: 汉明重量
        """
        return np.sum(vector != 0)

    def is_valid_codeword(self, codeword):
        """
        检查是否为有效码字

        参数:
            codeword: 候选码字

        返回:
            is_valid: 是否为有效码字
        """
        syndrome = np.dot(self.H, codeword) % 2
        return np.sum(syndrome) == 0

    def brute_force_search(self, max_weight=None):
        """
        暴力搜索法计算最小距离

        注意：仅适用于小码长，复杂度为O(2^k)

        参数:
            max_weight: 最大搜索重量（可选，用于提前终止）

        返回:
            min_distance: 最小汉明距离
            min_codeword: 对应的最小重量码字
        """
        print("\n使用暴力搜索法计算最小距离...")

        if self.k > 20:
            print(f"警告：k={self.k}太大，暴力搜索可能需要很长时间！")
            return None, None

        min_distance = float('inf')
        min_codeword = None
        total_codewords = 2**self.k

        print(f"需要检查 {total_codewords} 个码字...")

        start_time = time.time()

        # 遍历所有可能的信息位组合
        for i in range(1, total_codewords):  # 跳过全零码字
            # 将整数转换为二进制信息位
            info_bits = np.array([int(b) for b in format(i, f'0{self.k}b')], dtype=int)

            # 生成码字
            if self.G is not None:
                codeword = np.dot(info_bits, self.G) % 2
            else:
                # 如果没有生成矩阵，使用编码器
                encoder = QCLDPCEncoder(self.H)
                codeword = encoder.encode(info_bits)

            # 计算汉明重量
            weight = self.hamming_weight(codeword)

            # 更新最小距离
            if weight < min_distance:
                min_distance = weight
                min_codeword = codeword.copy()
                print(f"  找到更小重量: {weight}")

                # 如果达到最大搜索重量，提前终止
                if max_weight and weight <= max_weight:
                    break

            # 进度显示
            if i % max(1, total_codewords // 10) == 0:
                elapsed = time.time() - start_time
                progress = i / total_codewords * 100
                print(f"  进度: {progress:.1f}%, 当前最小距离: {min_distance}, 耗时: {elapsed:.1f}s")

        elapsed_time = time.time() - start_time
        print(f"暴力搜索完成，耗时: {elapsed_time:.2f}秒")

        return int(min_distance), min_codeword

    def syndrome_based_search(self, max_weight=10):
        """
        基于校验矩阵的搜索法

        通过枚举低重量向量并检查是否满足校验方程来寻找最小距离

        参数:
            max_weight: 最大搜索重量

        返回:
            min_distance: 最小汉明距离
            min_codeword: 对应的最小重量码字
        """
        print(f"\n使用基于校验矩阵的搜索法（最大重量={max_weight}）...")

        min_distance = float('inf')
        min_codeword = None

        start_time = time.time()

        # 从重量1开始搜索
        for weight in range(1, max_weight + 1):
            print(f"  搜索重量 {weight} 的码字...")

            found_codeword = False
            combinations_count = 0

            # 枚举所有重量为weight的向量
            for positions in itertools.combinations(range(self.n), weight):
                combinations_count += 1

                # 构造候选码字
                candidate = np.zeros(self.n, dtype=int)
                candidate[list(positions)] = 1

                # 检查是否为有效码字
                if self.is_valid_codeword(candidate):
                    min_distance = weight
                    min_codeword = candidate.copy()
                    found_codeword = True
                    print(f"    找到重量为 {weight} 的码字！")
                    break

                # 进度显示（对于大的组合数）
                if combinations_count % 10000 == 0:
                    elapsed = time.time() - start_time
                    print(f"    已检查 {combinations_count} 个组合，耗时: {elapsed:.1f}s")

            if found_codeword:
                break

            total_combinations = math.comb(self.n, weight)
            print(f"    重量 {weight}: 检查了 {combinations_count}/{total_combinations} 个组合")

        elapsed_time = time.time() - start_time
        print(f"基于校验矩阵的搜索完成，耗时: {elapsed_time:.2f}秒")

        if min_distance == float('inf'):
            print(f"在重量 ≤ {max_weight} 范围内未找到非零码字")
            return None, None

        return int(min_distance), min_codeword

    def random_search(self, num_samples=10000, max_weight=None):
        """
        随机搜索法（启发式方法）

        随机生成信息位并计算对应码字的重量

        参数:
            num_samples: 随机样本数量
            max_weight: 最大重量阈值（用于提前终止）

        返回:
            min_distance: 找到的最小汉明距离
            min_codeword: 对应的码字
        """
        print(f"\n使用随机搜索法（样本数={num_samples}）...")

        min_distance = float('inf')
        min_codeword = None

        start_time = time.time()

        # 初始化编码器
        encoder = QCLDPCEncoder(self.H)

        for i in range(num_samples):
            # 生成随机信息位（确保不是全零）
            while True:
                info_bits = np.random.randint(0, 2, self.k)
                if np.sum(info_bits) > 0:  # 确保不是全零
                    break

            # 编码
            codeword = encoder.encode(info_bits)

            # 计算汉明重量
            weight = self.hamming_weight(codeword)

            # 更新最小距离
            if weight < min_distance:
                min_distance = weight
                min_codeword = codeword.copy()
                print(f"  样本 {i+1}: 找到重量 {weight} 的码字")

                # 如果达到目标重量，提前终止
                if max_weight and weight <= max_weight:
                    break

            # 进度显示
            if (i + 1) % (num_samples // 10) == 0:
                elapsed = time.time() - start_time
                progress = (i + 1) / num_samples * 100
                print(f"  进度: {progress:.1f}%, 当前最小距离: {min_distance}, 耗时: {elapsed:.1f}s")

        elapsed_time = time.time() - start_time
        print(f"随机搜索完成，耗时: {elapsed_time:.2f}秒")

        return int(min_distance), min_codeword

    def estimate_minimum_distance(self):
        """
        估计最小汉明距离

        使用多种方法的组合来估计最小距离

        返回:
            results: 各种方法的结果字典
        """
        print("=" * 60)
        print("计算CCSDS (128,64) QC-LDPC码的最小汉明距离")
        print("=" * 60)

        results = {}

        # 方法1：随机搜索（快速估计）
        print("\n方法1：随机搜索")
        min_dist_random, codeword_random = self.random_search(num_samples=5000)
        results['random_search'] = {
            'distance': min_dist_random,
            'codeword': codeword_random
        }

        # 方法2：基于校验矩阵的搜索（精确但有限）
        print("\n方法2：基于校验矩阵的搜索")
        max_search_weight = min(8, min_dist_random) if min_dist_random else 8
        min_dist_syndrome, codeword_syndrome = self.syndrome_based_search(max_weight=max_search_weight)
        results['syndrome_search'] = {
            'distance': min_dist_syndrome,
            'codeword': codeword_syndrome
        }

        # 方法3：暴力搜索（仅在k较小时使用）
        if self.k <= 16:
            print("\n方法3：暴力搜索")
            min_dist_brute, codeword_brute = self.brute_force_search()
            results['brute_force'] = {
                'distance': min_dist_brute,
                'codeword': codeword_brute
            }
        else:
            print(f"\n方法3：暴力搜索跳过（k={self.k}太大）")
            results['brute_force'] = {
                'distance': None,
                'codeword': None
            }

        return results


def analyze_distance_results(results):
    """
    分析最小距离计算结果

    参数:
        results: 计算结果字典
    """
    print("\n" + "=" * 60)
    print("最小汉明距离计算结果分析")
    print("=" * 60)

    distances = []

    for method, result in results.items():
        distance = result['distance']
        codeword = result['codeword']

        print(f"\n{method.replace('_', ' ').title()}:")
        if distance is not None:
            print(f"  最小距离: {distance}")
            if codeword is not None:
                print(f"  码字重量: {np.sum(codeword)}")
                print(f"  码字前16位: {codeword[:16]}")
            distances.append(distance)
        else:
            print("  未计算或未找到结果")

    if distances:
        min_distance = min(distances)
        max_distance = max(distances)

        print(f"\n总结:")
        print(f"  所有方法中的最小距离: {min_distance}")
        if min_distance != max_distance:
            print(f"  最大距离: {max_distance}")
            print(f"  注意：不同方法给出了不同结果，建议使用更精确的方法验证")
        else:
            print(f"  所有方法一致，最小距离为: {min_distance}")

        # 理论分析
        print(f"\n理论分析:")
        print(f"  纠错能力: t = ⌊(d_min-1)/2⌋ = {(min_distance-1)//2}")
        print(f"  检错能力: e = d_min-1 = {min_distance-1}")

        return min_distance
    else:
        print("\n未能计算出最小距离")
        return None


def main():
    """
    主函数
    """
    print("CCSDS (128,64) QC-LDPC码最小汉明距离计算")
    print("=" * 60)

    # 构造QC-LDPC码
    constructor = QCLDPCConstructor()

    # CCSDS (128,64) LDPC码的原型矩阵
    prototype_matrix = np.array([
        [0, -1, -1, -1, 0, -1, -1, -1],
        [-1, 0, -1, -1, -1, 0, -1, -1],
        [-1, -1, 0, -1, -1, -1, 0, -1],
        [-1, -1, -1, 0, -1, -1, -1, 0]
    ])

    H = constructor.construct_H_matrix(prototype_matrix)

    # 获取生成矩阵
    encoder = QCLDPCEncoder(H)
    G = encoder.G

    # 创建计算器
    calculator = MinimumDistanceCalculator(H, G)

    # 计算最小距离
    results = calculator.estimate_minimum_distance()

    # 分析结果
    min_distance = analyze_distance_results(results)

    print("\n" + "=" * 60)
    print("计算完成！")
    print("=" * 60)

    return min_distance, results


if __name__ == "__main__":
    # 设置随机种子
    np.random.seed(42)

    # 运行计算
    min_distance, results = main()
