"""
SC-LDPC编解码器简单使用示例

本脚本展示了SC-LDPC码的基本使用方法，包括：
- 构造SC-LDPC码
- 编码和解码
- 性能比较

作者：AI Assistant
日期：2024
"""

import numpy as np
from ldpc import *

def simple_sc_ldpc_example():
    """简单的SC-LDPC使用示例"""
    print("=== SC-LDPC简单使用示例 ===\n")

    # 1. 构造SC-LDPC码
    print("1. 构造SC-LDPC码")

    # 定义基础原型矩阵
    base_matrix = np.array([
        [1, 1, 0],
        [0, 1, 1]
    ])

    print(f"基础矩阵:\n{base_matrix}")

    # 构造空间耦合矩阵
    coupling_length = 4
    memory_length = 1

    H_coupled = generate_sc_ldpc_matrix(base_matrix, coupling_length, memory_length)
    print(f"耦合矩阵大小: {H_coupled.shape}")
    print(f"耦合矩阵:\n{H_coupled}")
    print()

    # 2. 创建编解码器
    print("2. 创建编解码器")

    encoder = SCLDPCEncoder(H_coupled)
    decoder = SCLDPCDecoder(H_coupled)

    params = encoder.get_code_parameters()
    print(f"码参数: n={params['n']}, k={params['k']}, 码率={params['rate']:.3f}")
    print()

    # 3. 编码示例
    print("3. 编码示例")

    if params['k'] > 0:
        # 生成随机信息位
        info_bits = np.random.randint(0, 2, params['k'])
        print(f"信息位: {info_bits}")

        # 编码
        codeword = encoder.encode(info_bits)
        print(f"码字: {codeword}")

        # 验证码字
        is_valid = decoder.is_valid_codeword(codeword)
        print(f"码字有效性: {is_valid}")
        print()

        # 4. 解码示例
        print("4. 解码示例")

        # 4.1 无噪声软解码
        print("4.1 无噪声软解码")
        symbols = bits_to_bpsk(codeword)
        llr_perfect = bpsk_to_llr(symbols, 0.001)

        decoded_soft, converged, iterations = decoder.decode_soft(llr_perfect)
        print(f"软解码结果: {decoded_soft}")
        print(f"收敛: {converged}, 迭代次数: {iterations}")

        if len(decoded_soft) >= params['k']:
            decoded_info_soft = decoded_soft[:params['k']]
            ber_soft = calculate_ber(info_bits, decoded_info_soft)
            print(f"软解码误码率: {ber_soft:.4f}")
        print()

        # 4.2 硬解码
        print("4.2 硬解码")
        # 添加一些错误
        received_hard = codeword.copy()
        if len(received_hard) > 1:
            error_pos = np.random.choice(len(received_hard), size=1, replace=False)
            received_hard[error_pos] = 1 - received_hard[error_pos]
            print(f"添加错误位置: {error_pos}")

        print(f"接收硬比特: {received_hard}")
        decoded_hard, converged_hard, iter_hard = decoder.decode_hard(received_hard)
        print(f"硬解码结果: {decoded_hard}")
        print(f"收敛: {converged_hard}, 迭代次数: {iter_hard}")

        if len(decoded_hard) >= params['k']:
            decoded_info_hard = decoded_hard[:params['k']]
            ber_hard = calculate_ber(info_bits, decoded_info_hard)
            print(f"硬解码误码率: {ber_hard:.4f}")
        print()

        # 5. 有噪声性能测试
        print("5. 有噪声性能测试")

        snr_values = [1, 3, 5]
        print("SNR(dB) | 软解码BER | 硬解码BER")
        print("-" * 35)

        for snr_db in snr_values:
            # 通过AWGN信道
            symbols = bits_to_bpsk(codeword)
            received_signal, noise_var = awgn_channel(symbols, snr_db)

            # 软解码
            llr = bpsk_to_llr(received_signal, noise_var)
            decoded_soft_noisy, _, _ = decoder.decode_soft(llr)

            if len(decoded_soft_noisy) >= params['k']:
                decoded_info_soft_noisy = decoded_soft_noisy[:params['k']]
                ber_soft_noisy = calculate_ber(info_bits, decoded_info_soft_noisy)
            else:
                ber_soft_noisy = 1.0

            # 硬解码
            hard_bits = (received_signal < 0).astype(int)
            decoded_hard_noisy, _, _ = decoder.decode_hard(hard_bits)

            if len(decoded_hard_noisy) >= params['k']:
                decoded_info_hard_noisy = decoded_hard_noisy[:params['k']]
                ber_hard_noisy = calculate_ber(info_bits, decoded_info_hard_noisy)
            else:
                ber_hard_noisy = 1.0

            print(f"{snr_db:7.1f} | {ber_soft_noisy:>9.4f} | {ber_hard_noisy:>9.4f}")

    else:
        print("警告：信息位数为0，跳过编解码示例")

def compare_ldpc_vs_sc_ldpc():
    """比较传统LDPC和SC-LDPC的性能"""
    print("\n=== 传统LDPC vs SC-LDPC性能比较 ===\n")

    # 1. 创建传统LDPC码
    print("1. 创建传统LDPC码")
    H_ldpc = np.array([
        [1, 1, 0, 1, 0, 0],
        [0, 1, 1, 0, 1, 0],
        [1, 0, 1, 0, 0, 1]
    ])

    encoder_ldpc = LDPCEncoder(H_ldpc)
    decoder_ldpc = LDPCDecoder(H_ldpc)
    params_ldpc = encoder_ldpc.get_code_parameters()

    print(f"传统LDPC: n={params_ldpc['n']}, k={params_ldpc['k']}, 码率={params_ldpc['rate']:.3f}")

    # 2. 创建SC-LDPC码
    print("2. 创建SC-LDPC码")
    base_matrix = np.array([
        [1, 1],
        [1, 0]
    ])

    H_sc_ldpc = generate_sc_ldpc_matrix(base_matrix, coupling_length=3, memory_length=1)
    encoder_sc_ldpc = SCLDPCEncoder(H_sc_ldpc)
    decoder_sc_ldpc = SCLDPCDecoder(H_sc_ldpc)
    params_sc_ldpc = encoder_sc_ldpc.get_code_parameters()

    print(f"SC-LDPC: n={params_sc_ldpc['n']}, k={params_sc_ldpc['k']}, 码率={params_sc_ldpc['rate']:.3f}")
    print()

    # 3. 性能比较
    print("3. 性能比较")

    if params_ldpc['k'] > 0 and params_sc_ldpc['k'] > 0:
        snr_test = 2
        num_tests = 10

        print(f"测试条件: SNR={snr_test}dB, 测试次数={num_tests}")
        print()

        # 传统LDPC测试
        ldpc_errors = 0
        for _ in range(num_tests):
            info_bits = np.random.randint(0, 2, params_ldpc['k'])
            codeword = encoder_ldpc.encode(info_bits)
            symbols = bits_to_bpsk(codeword)
            received_signal, noise_var = awgn_channel(symbols, snr_test)
            llr = bpsk_to_llr(received_signal, noise_var)
            decoded_bits, _, _ = decoder_ldpc.decode(llr)
            decoded_info = decoded_bits[:params_ldpc['k']]
            ldpc_errors += np.sum(info_bits != decoded_info)

        ldpc_ber = ldpc_errors / (num_tests * params_ldpc['k'])

        # SC-LDPC测试
        sc_ldpc_errors = 0
        for _ in range(num_tests):
            info_bits = np.random.randint(0, 2, params_sc_ldpc['k'])
            codeword = encoder_sc_ldpc.encode(info_bits)
            symbols = bits_to_bpsk(codeword)
            received_signal, noise_var = awgn_channel(symbols, snr_test)
            llr = bpsk_to_llr(received_signal, noise_var)

            # 确保LLR长度与解码器期望的长度匹配
            if len(llr) != params_sc_ldpc['n']:
                # 调整LLR长度
                if len(llr) > params_sc_ldpc['n']:
                    llr = llr[:params_sc_ldpc['n']]
                else:
                    llr = np.pad(llr, (0, params_sc_ldpc['n'] - len(llr)), 'constant')

            decoded_bits, _, _ = decoder_sc_ldpc.decode_soft(llr)
            if len(decoded_bits) >= params_sc_ldpc['k']:
                decoded_info = decoded_bits[:params_sc_ldpc['k']]
                sc_ldpc_errors += np.sum(info_bits != decoded_info)
            else:
                sc_ldpc_errors += params_sc_ldpc['k']  # 全部错误

        sc_ldpc_ber = sc_ldpc_errors / (num_tests * params_sc_ldpc['k'])

        print("结果:")
        print(f"传统LDPC BER: {ldpc_ber:.4f}")
        print(f"SC-LDPC BER:  {sc_ldpc_ber:.4f}")

        if sc_ldpc_ber < ldpc_ber:
            print("✓ SC-LDPC性能更好")
        elif sc_ldpc_ber > ldpc_ber:
            print("✓ 传统LDPC性能更好")
        else:
            print("= 性能相当")
    else:
        print("无法进行比较：某个码的信息位数为0")

def main():
    """主函数"""
    print("SC-LDPC编解码器使用示例\n")
    print("=" * 50)

    try:
        # 基本示例
        simple_sc_ldpc_example()

        # 性能比较
        compare_ldpc_vs_sc_ldpc()

        print("\n" + "=" * 50)
        print("示例运行完成！")

    except Exception as e:
        print(f"运行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
