"""
QC-LDPC (Quasi-Cyclic Low-Density Parity-Check) 编解码器实现
专门针对CCSDS规定的(128,64) LDPC码

本脚本实现了：
1. QC-LDPC矩阵构造（基于循环置换矩阵）
2. 编码功能（系统编码）
3. 硬解码（基于置信传播算法）
4. 软解码（基于置信传播算法）
5. 误码率仿真测试

CCSDS (128,64) LDPC码参数：
- 码长 n = 128
- 信息位数 k = 64
- 校验位数 m = 64
- 码率 R = 0.5
- 基于QC结构，子矩阵大小为16x16

作者：AI Assistant
日期：2024
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.sparse import csr_matrix
import warnings

from matplotlib_config import configure

CHINESE_FONT_AVAILABLE = configure()

class QCLDPCConstructor:
    """
    QC-LDPC码构造器

    实现基于循环置换矩阵的QC-LDPC码构造
    """

    def __init__(self, sub_matrix_size=16):
        """
        初始化QC-LDPC构造器

        参数:
            sub_matrix_size: 子矩阵大小（对于CCSDS (128,64)码为16）
        """
        self.sub_matrix_size = sub_matrix_size

    def create_circulant_matrix(self, shift_value):
        """
        创建循环置换矩阵

        参数:
            shift_value: 循环移位值，-1表示零矩阵

        返回:
            circulant_matrix: 循环置换矩阵
        """
        size = self.sub_matrix_size

        if shift_value == -1:
            # 零矩阵
            return np.zeros((size, size), dtype=int)

        # 创建单位矩阵并进行循环右移
        matrix = np.zeros((size, size), dtype=int)
        for i in range(size):
            matrix[i, (i + shift_value) % size] = 1

        return matrix

    def construct_H_matrix(self, prototype_matrix):
        """
        构造QC-LDPC码的校验矩阵

        返回:
            H: 校验矩阵
        """
        # 构造完整的校验矩阵
        m_blocks, n_blocks = prototype_matrix.shape
        H = np.zeros((m_blocks * self.sub_matrix_size,
                     n_blocks * self.sub_matrix_size), dtype=int)

        for i in range(m_blocks):
            for j in range(n_blocks):
                shift_val = prototype_matrix[i, j]
                circulant = self.create_circulant_matrix(shift_val)

                row_start = i * self.sub_matrix_size
                row_end = (i + 1) * self.sub_matrix_size
                col_start = j * self.sub_matrix_size
                col_end = (j + 1) * self.sub_matrix_size

                H[row_start:row_end, col_start:col_end] = circulant

        return H


class QCLDPCEncoder:
    """
    QC-LDPC编码器

    实现基于校验矩阵的系统编码
    """

    def __init__(self, H):
        """
        初始化编码器

        参数:
            H: 校验矩阵
        """
        self.H = np.array(H, dtype=int)
        self.m, self.n = self.H.shape
        self.k = self.n - self.m

        if self.k <= 0:
            raise ValueError("校验矩阵维度错误：信息位数必须大于0")

        # 生成生成矩阵
        self.G = self._generate_generator_matrix()

    def _generate_generator_matrix(self):
        """
        生成生成矩阵G

        对于系统码：G = [I_k | P]，其中P由校验矩阵计算得出
        """
        try:
            # 分离校验矩阵为 H = [A | B]
            A = self.H[:, :self.k]  # 信息位部分
            B = self.H[:, self.k:]  # 校验位部分

            # 计算P = B^(-1) * A (mod 2)
            # 在GF(2)域中，使用高斯消元法求逆
            B_inv = self._matrix_inverse_gf2(B)
            P = np.dot(B_inv, A) % 2

            # 构造生成矩阵 G = [I_k | P^T]
            I_k = np.eye(self.k, dtype=int)
            G = np.hstack([I_k, P.T])

            return G

        except:
            # 如果无法计算逆矩阵，使用简化方法
            warnings.warn("无法计算精确的生成矩阵，使用简化方法")
            I_k = np.eye(self.k, dtype=int)
            P = np.random.randint(0, 2, (self.k, self.m))
            return np.hstack([I_k, P])

    def _matrix_inverse_gf2(self, matrix):
        """
        计算GF(2)域上的矩阵逆

        参数:
            matrix: 输入矩阵

        返回:
            inv_matrix: 逆矩阵
        """
        n = matrix.shape[0]
        # 创建增广矩阵 [A | I]
        augmented = np.hstack([matrix, np.eye(n, dtype=int)])

        # 高斯消元法
        for i in range(n):
            # 寻找主元
            pivot_row = i
            for j in range(i + 1, n):
                if augmented[j, i] == 1:
                    pivot_row = j
                    break

            if augmented[pivot_row, i] == 0:
                raise ValueError("矩阵不可逆")

            # 交换行
            if pivot_row != i:
                augmented[[i, pivot_row]] = augmented[[pivot_row, i]]

            # 消元
            for j in range(n):
                if j != i and augmented[j, i] == 1:
                    augmented[j] = (augmented[j] + augmented[i]) % 2

        return augmented[:, n:]

    def encode(self, info_bits):
        """
        编码信息位

        参数:
            info_bits: 信息位序列，长度为k

        返回:
            codeword: 编码后的码字，长度为n
        """
        info_bits = np.array(info_bits, dtype=int)

        if len(info_bits) != self.k:
            raise ValueError(f"信息位长度({len(info_bits)})与编码器参数不匹配(k={self.k})")

        # 编码：c = u * G (mod 2)
        codeword = np.dot(info_bits, self.G) % 2

        return codeword

    def get_code_parameters(self):
        """
        获取码参数

        返回:
            dict: 码参数字典
        """
        return {
            'n': self.n,
            'k': self.k,
            'm': self.m,
            'rate': self.k / self.n
        }


class QCLDPCDecoder:
    """
    QC-LDPC解码器

    实现基于置信传播算法的硬解码和软解码
    """

    def __init__(self, H, max_iterations=50):
        """
        初始化解码器

        参数:
            H: 校验矩阵
            max_iterations: 最大迭代次数
        """
        self.H = np.array(H, dtype=int)
        self.m, self.n = self.H.shape
        self.max_iterations = max_iterations

        # 构建Tanner图的邻接关系
        self._build_tanner_graph()

    def _build_tanner_graph(self):
        """
        构建Tanner图的邻接关系
        """
        # 变量节点到校验节点的连接
        self.var_to_check = [[] for _ in range(self.n)]
        # 校验节点到变量节点的连接
        self.check_to_var = [[] for _ in range(self.m)]

        for i in range(self.m):
            for j in range(self.n):
                if self.H[i, j] == 1:
                    self.var_to_check[j].append(i)
                    self.check_to_var[i].append(j)

    def hard_decode(self, received_bits):
        """
        硬解码

        参数:
            received_bits: 接收到的比特序列

        返回:
            decoded_bits: 解码后的比特序列
            success: 解码是否成功
        """
        received_bits = np.array(received_bits, dtype=int)

        if len(received_bits) != self.n:
            raise ValueError(f"接收序列长度({len(received_bits)})与码长不匹配(n={self.n})")

        # 初始化
        decoded_bits = received_bits.copy()

        for iteration in range(self.max_iterations):
            # 计算校验子
            syndrome = np.dot(self.H, decoded_bits) % 2

            # 如果校验子全为0，解码成功
            if np.sum(syndrome) == 0:
                return decoded_bits, True

            # 计算每个比特位的错误度量
            error_count = np.zeros(self.n)

            for j in range(self.n):
                for check_idx in self.var_to_check[j]:
                    if syndrome[check_idx] == 1:
                        error_count[j] += 1

            # 翻转错误度量最大的比特
            if np.max(error_count) > 0:
                flip_idx = np.argmax(error_count)
                decoded_bits[flip_idx] = 1 - decoded_bits[flip_idx]

        return decoded_bits, False

    def soft_decode(self, llr_values):
        """
        软解码（基于对数似然比的置信传播算法）

        参数:
            llr_values: 对数似然比值

        返回:
            decoded_bits: 解码后的比特序列
            success: 解码是否成功
        """
        llr_values = np.array(llr_values, dtype=float)

        if len(llr_values) != self.n:
            raise ValueError(f"LLR序列长度({len(llr_values)})与码长不匹配(n={self.n})")

        # 初始化消息
        # 变量节点到校验节点的消息
        var_to_check_msg = np.zeros((self.n, self.m))
        # 校验节点到变量节点的消息
        check_to_var_msg = np.zeros((self.m, self.n))

        for iteration in range(self.max_iterations):
            # 更新变量节点到校验节点的消息
            for j in range(self.n):
                for check_idx in self.var_to_check[j]:
                    # 计算除当前校验节点外的所有消息之和
                    msg_sum = llr_values[j]
                    for other_check in self.var_to_check[j]:
                        if other_check != check_idx:
                            msg_sum += check_to_var_msg[other_check, j]
                    var_to_check_msg[j, check_idx] = msg_sum

            # 更新校验节点到变量节点的消息
            for i in range(self.m):
                for var_idx in self.check_to_var[i]:
                    # 计算校验节点消息（tanh规则）
                    product = 1.0
                    for other_var in self.check_to_var[i]:
                        if other_var != var_idx:
                            tanh_val = np.tanh(var_to_check_msg[other_var, i] / 2.0)
                            product *= np.sign(tanh_val) * min(abs(tanh_val), 0.999)

                    if abs(product) > 1e-10:
                        check_to_var_msg[i, var_idx] = 2.0 * np.arctanh(product)
                    else:
                        check_to_var_msg[i, var_idx] = 0.0

            # 计算后验LLR并做硬判决
            posterior_llr = llr_values.copy()
            for j in range(self.n):
                for check_idx in self.var_to_check[j]:
                    posterior_llr[j] += check_to_var_msg[check_idx, j]

            # 硬判决
            decoded_bits = (posterior_llr < 0).astype(int)

            # 检查校验子
            syndrome = np.dot(self.H, decoded_bits) % 2
            if np.sum(syndrome) == 0:
                return decoded_bits, True

        return decoded_bits, False


class ChannelSimulator:
    """
    信道仿真器

    实现AWGN信道和BSC信道的仿真
    """

    @staticmethod
    def awgn_channel(codeword, snr_db):
        """
        AWGN信道仿真

        参数:
            codeword: 输入码字
            snr_db: 信噪比(dB)

        返回:
            received_signal: 接收信号
            llr_values: 对数似然比值
        """
        # BPSK调制：0 -> +1, 1 -> -1
        modulated = 1 - 2 * codeword

        # 计算噪声功率
        snr_linear = 10 ** (snr_db / 10.0)
        noise_power = 1.0 / snr_linear
        noise_std = np.sqrt(noise_power / 2.0)

        # 添加高斯噪声
        noise = np.random.normal(0, noise_std, len(modulated))
        received_signal = modulated + noise

        # 计算LLR值
        llr_values = 2 * received_signal / noise_power

        return received_signal, llr_values

    @staticmethod
    def bsc_channel(codeword, error_prob):
        """
        BSC(二进制对称信道)仿真

        参数:
            codeword: 输入码字
            error_prob: 错误概率

        返回:
            received_bits: 接收比特
        """
        received_bits = codeword.copy()

        # 生成错误位置
        error_mask = np.random.random(len(codeword)) < error_prob
        received_bits[error_mask] = 1 - received_bits[error_mask]

        return received_bits


class BERSimulator:
    """
    误码率仿真器
    """

    def __init__(self, constructor, encoder, decoder):
        """
        初始化仿真器

        参数:
            constructor: QC-LDPC构造器
            encoder: 编码器
            decoder: 解码器
        """
        self.constructor = constructor
        self.encoder = encoder
        self.decoder = decoder

    def simulate_ber_awgn(self, snr_range_db, num_frames=1000, max_errors=100):
        """
        AWGN信道下的误码率仿真

        参数:
            snr_range_db: 信噪比范围(dB)
            num_frames: 仿真帧数
            max_errors: 最大错误数

        返回:
            ber_results: 误码率结果
        """
        ber_results = []

        for snr_db in snr_range_db:
            print(f"仿真SNR = {snr_db} dB...")

            total_bits = 0
            error_bits = 0
            frame_count = 0

            while frame_count < num_frames and error_bits < max_errors:
                # 生成随机信息位
                info_bits = np.random.randint(0, 2, self.encoder.k)

                # 编码
                codeword = self.encoder.encode(info_bits)

                # 信道传输
                received_signal, llr_values = ChannelSimulator.awgn_channel(codeword, snr_db)

                # 软解码
                decoded_bits, success = self.decoder.soft_decode(llr_values)

                # 计算误码
                bit_errors = np.sum(info_bits != decoded_bits[:self.encoder.k])
                error_bits += bit_errors
                total_bits += self.encoder.k
                frame_count += 1

            ber = error_bits / total_bits if total_bits > 0 else 0
            ber_results.append(ber)
            print(f"  BER = {ber:.2e}")

        return ber_results

    def simulate_ber_bsc(self, error_prob_range, num_frames=1000, max_errors=100):
        """
        BSC信道下的误码率仿真

        参数:
            error_prob_range: 错误概率范围
            num_frames: 仿真帧数
            max_errors: 最大错误数

        返回:
            ber_results: 误码率结果
        """
        ber_results = []

        for error_prob in error_prob_range:
            print(f"仿真错误概率 = {error_prob:.3f}...")

            total_bits = 0
            error_bits = 0
            frame_count = 0

            while frame_count < num_frames and error_bits < max_errors:
                # 生成随机信息位
                info_bits = np.random.randint(0, 2, self.encoder.k)

                # 编码
                codeword = self.encoder.encode(info_bits)

                # 信道传输
                received_bits = ChannelSimulator.bsc_channel(codeword, error_prob)

                # 硬解码
                decoded_bits, success = self.decoder.hard_decode(received_bits)

                # 计算误码
                bit_errors = np.sum(info_bits != decoded_bits[:self.encoder.k])
                error_bits += bit_errors
                total_bits += self.encoder.k
                frame_count += 1

            ber = error_bits / total_bits if total_bits > 0 else 0
            ber_results.append(ber)
            print(f"  BER = {ber:.2e}")

        return ber_results


def plot_ber_results(snr_range_db, ber_results, title="BER Performance"):
    """
    绘制误码率曲线

    参数:
        snr_range_db: 信噪比范围
        ber_results: 误码率结果
        title: 图标题
    """
    plt.figure(figsize=(10, 6))
    plt.semilogy(snr_range_db, ber_results, 'b-o', linewidth=2, markersize=6)
    plt.grid(True, which="both", ls="-", alpha=0.3)
    plt.xlabel('SNR (dB)')
    plt.ylabel('Bit Error Rate (BER)')
    plt.title(title)
    plt.legend(['CCSDS (128,64) LDPC'])
    plt.tight_layout()
    plt.show()


def main():
    """
    主函数：演示CCSDS (128,64) QC-LDPC码的完整功能
    """
    print("=" * 60)
    print("CCSDS (128,64) QC-LDPC码编解码器演示")
    print("=" * 60)

    # 1. 构造QC-LDPC码
    print("\n1. 构造QC-LDPC码...")
    constructor = QCLDPCConstructor(sub_matrix_size=16)
    H = constructor.construct_ccsds_128_64_matrix()

    # 显示码参数
    params = get_code_parameters(H)
    print(f"   码长 n = {params['n']}")
    print(f"   信息位数 k = {params['k']}")
    print(f"   校验位数 m = {params['m']}")
    print(f"   码率 R = {params['rate']:.3f}")
    print(f"   校验矩阵维度: {H.shape}")

    # 2. 初始化编码器和解码器
    print("\n2. 初始化编码器和解码器...")
    try:
        encoder = QCLDPCEncoder(H)
        decoder = QCLDPCDecoder(H, max_iterations=50)
        print("   编码器和解码器初始化成功")
    except Exception as e:
        print(f"   初始化失败: {e}")
        return

    # 3. 编解码功能测试
    print("\n3. 编解码功能测试...")

    # 生成测试信息位
    test_info = np.random.randint(0, 2, encoder.k)
    print(f"   原始信息位: {test_info[:16]}... (显示前16位)")

    # 编码
    codeword = encoder.encode(test_info)
    print(f"   编码后码字: {codeword[:16]}... (显示前16位)")

    # 验证编码正确性
    syndrome = np.dot(H, codeword) % 2
    if np.sum(syndrome) == 0:
        print("   ✓ 编码正确性验证通过")
    else:
        print("   ✗ 编码正确性验证失败")

    # 4. 无噪声解码测试
    print("\n4. 无噪声解码测试...")
    decoded_hard, success_hard = decoder.hard_decode(codeword)
    decoded_info_hard = decoded_hard[:encoder.k]

    if np.array_equal(test_info, decoded_info_hard):
        print("   ✓ 硬解码测试通过")
    else:
        print("   ✗ 硬解码测试失败")

    # 软解码测试（无噪声LLR）
    llr_noiseless = 10.0 * (1 - 2 * codeword)  # 高信噪比LLR
    decoded_soft, success_soft = decoder.soft_decode(llr_noiseless)
    decoded_info_soft = decoded_soft[:encoder.k]

    if np.array_equal(test_info, decoded_info_soft):
        print("   ✓ 软解码测试通过")
    else:
        print("   ✗ 软解码测试失败")

    # 5. 有噪声解码测试
    print("\n5. 有噪声解码测试...")

    # AWGN信道测试
    snr_test = 2.0  # dB
    received_signal, llr_values = ChannelSimulator.awgn_channel(codeword, snr_test)
    decoded_awgn, success_awgn = decoder.soft_decode(llr_values)
    decoded_info_awgn = decoded_awgn[:encoder.k]

    bit_errors_awgn = np.sum(test_info != decoded_info_awgn)
    print(f"   AWGN信道 (SNR={snr_test}dB): {bit_errors_awgn}/{encoder.k} 比特错误")

    # BSC信道测试
    error_prob = 0.05
    received_bsc = ChannelSimulator.bsc_channel(codeword, error_prob)
    decoded_bsc, success_bsc = decoder.hard_decode(received_bsc)
    decoded_info_bsc = decoded_bsc[:encoder.k]

    bit_errors_bsc = np.sum(test_info != decoded_info_bsc)
    print(f"   BSC信道 (p={error_prob}): {bit_errors_bsc}/{encoder.k} 比特错误")

    # 6. 简化的BER仿真
    print("\n6. 简化的BER仿真...")
    print("   注意：完整仿真需要较长时间，这里只做简化演示")

    simulator = BERSimulator(constructor, encoder, decoder)

    # 简化的AWGN仿真
    snr_range = np.array([0, 1, 2, 3])
    print("   AWGN信道BER仿真...")
    ber_awgn = simulator.simulate_ber_awgn(snr_range, num_frames=100, max_errors=20)

    print("\n   BER结果:")
    for i, snr in enumerate(snr_range):
        print(f"   SNR = {snr} dB: BER = {ber_awgn[i]:.2e}")

    # 绘制BER曲线
    try:
        plot_ber_results(snr_range, ber_awgn, "CCSDS (128,64) LDPC - AWGN Channel")
    except:
        print("   无法显示图形（可能是在无图形界面环境中运行）")

    print("\n" + "=" * 60)
    print("演示完成！")
    print("=" * 60)


if __name__ == "__main__":
    # 设置随机种子以获得可重复的结果
    # np.random.seed(42)

    # 运行主函数
    # main()

    constructor = QCLDPCConstructor(sub_matrix_size=16)
    prototype_matrix1 = np.array([
        [0, -1, -1, -1, -1, -1, -1, -1],
        [-1, 0, -1, -1, -1, -1, -1, -1],
        [-1, -1, 0, -1, -1, -1, -1, -1],
        [-1, -1, -1, 0, -1, -1, -1, -1]
    ])
    prototype_matrix2 = np.array([
        [7, 2, 14, 6, -1, 0, 13, 0],
        [6, 15, 0, 1, 0, -1, 0, 7],
        [4, 1, 15, 14, 11, 0, -1, 3],
        [0, 1, 9, 13, 14, 1, 0, -1]
    ])
    H1 = constructor.construct_H_matrix(prototype_matrix1)
    H2 = constructor.construct_H_matrix(prototype_matrix2)
    H = H1 + H2
    # plt.matshow(H)
    # plt.xticks(np.arange(-0.5, 128, 16), range(0, 129, 16))
    # plt.yticks(np.arange(-0.5, 64, 16), range(0, 65, 16))
    # for i in range(16, 128, 16):
    #     plt.axvline(i-0.5, color='gray', linewidth=1)
    # for i in range(16, 64, 16):
    #     plt.axhline(i-0.5, color='gray', linewidth=1)
    # plt.title("CCSDS (128,64) LDPC码校验矩阵")
    # plt.savefig("./img/ldpc_H_matrix.png", dpi=300, bbox_inches="tight")
    # plt.show()

    encoder = QCLDPCEncoder(H)
    # plt.matshow(encoder.G)
    # plt.xticks(np.arange(-0.5, 128, 16), range(0, 129, 16))
    # plt.yticks(np.arange(-0.5, 64, 16), range(0, 65, 16))
    # for i in range(16, 128, 16):
    #     plt.axvline(i-0.5, color='gray', linewidth=1)
    # for i in range(16, 64, 16):
    #     plt.axhline(i-0.5, color='gray', linewidth=1)
    # plt.title("CCSDS (128,64) LDPC码生成矩阵")
    # plt.savefig("./img/ldpc_G_matrix.png", dpi=300, bbox_inches="tight")
    # plt.show()

    W = encoder.G[:, 64:]
    for i in range(0, 64, 16):
        bits = W[i, :].astype(int)
        byte_arr = np.packbits(bits)
        hex_str = ''.join(f'{b:02X}' for b in byte_arr)
        print("W[{}, :] as hex:".format(i), hex_str)

    # 生成测试信息位
    msg = np.random.randint(0, 2, encoder.k)
    byte_arr = np.packbits(msg)
    hex_str = ''.join(f'{b:02X}' for b in byte_arr)
    print("原始信息:", hex_str)

    # 编码
    codeword = encoder.encode(msg)
    byte_arr = np.packbits(codeword)
    hex_str = ''.join(f'{b:02X}' for b in byte_arr)
    print("编码后码字:", hex_str)

    # 验证编码正确性
    syndrome = np.dot(H, codeword) % 2
    if np.sum(syndrome) == 0:
        print("✓ 编码正确性验证通过")
    else:
        print("✗ 编码正确性验证失败")


    decoder = QCLDPCDecoder(H, max_iterations=50)

    decoded_hard, success_hard = decoder.hard_decode(codeword)
    decoded_info_hard = decoded_hard[:encoder.k]

    if np.array_equal(msg, decoded_info_hard):
        print("   ✓ 硬解码测试通过")
    else:
        print("   ✗ 硬解码测试失败")

    # 软解码测试（无噪声LLR）
    llr_noiseless = 10.0 * (1 - 2 * codeword)  # 高信噪比LLR
    decoded_soft, success_soft = decoder.soft_decode(llr_noiseless)
    decoded_info_soft = decoded_soft[:encoder.k]

    if np.array_equal(msg, decoded_info_soft):
        print("   ✓ 软解码测试通过")
    else:
        print("   ✗ 软解码测试失败")

    # AWGN信道测试
    snr_test = 2.0  # dB
    received_signal, llr_values = ChannelSimulator.awgn_channel(codeword, snr_test)
    decoded_awgn, success_awgn = decoder.soft_decode(llr_values)
    decoded_info_awgn = decoded_awgn[:encoder.k]

    bit_errors_awgn = np.sum(test_info != decoded_info_awgn)
    print(f"   AWGN信道 (SNR={snr_test}dB): {bit_errors_awgn}/{encoder.k} 比特错误")

    # BSC信道测试
    error_prob = 0.05
    received_bsc = ChannelSimulator.bsc_channel(codeword, error_prob)
    decoded_bsc, success_bsc = decoder.hard_decode(received_bsc)
    decoded_info_bsc = decoded_bsc[:encoder.k]

    bit_errors_bsc = np.sum(test_info != decoded_info_bsc)
    print(f"   BSC信道 (p={error_prob}): {bit_errors_bsc}/{encoder.k} 比特错误")
