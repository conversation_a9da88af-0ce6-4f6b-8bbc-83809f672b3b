"""
简单的CCSDS (128,64) QC-LDPC码测试脚本

快速验证编解码功能的正确性

作者：AI Assistant
日期：2024
"""

import numpy as np
from qc_ldpc_ccsds import QCLDPCConstructor, QCLDPCEncoder, QCLDPCDecoder, ChannelSimulator

def simple_test():
    """
    简单的功能测试
    """
    print("CCSDS (128,64) QC-LDPC码简单测试")
    print("=" * 50)
    
    # 1. 构造码
    print("1. 构造QC-LDPC码...")
    constructor = QCLDPCConstructor(sub_matrix_size=16)
    H = constructor.construct_ccsds_128_64_matrix()
    
    params = constructor.get_code_parameters(H)
    print(f"   码参数: n={params['n']}, k={params['k']}, m={params['m']}, R={params['rate']:.3f}")
    
    # 2. 初始化编解码器
    print("\n2. 初始化编解码器...")
    encoder = QCLDPCEncoder(H)
    decoder = QCLDPCDecoder(H, max_iterations=50)
    print("   编解码器初始化完成")
    
    # 3. 基本编解码测试
    print("\n3. 基本编解码测试...")
    
    # 测试用例1：全零信息位
    info_zeros = np.zeros(64, dtype=int)
    codeword_zeros = encoder.encode(info_zeros)
    decoded_zeros, success_zeros = decoder.hard_decode(codeword_zeros)
    
    if np.array_equal(info_zeros, decoded_zeros[:64]) and success_zeros:
        print("   ✓ 全零信息位测试通过")
    else:
        print("   ✗ 全零信息位测试失败")
    
    # 测试用例2：随机信息位
    np.random.seed(123)
    info_random = np.random.randint(0, 2, 64)
    codeword_random = encoder.encode(info_random)
    decoded_random, success_random = decoder.hard_decode(codeword_random)
    
    if np.array_equal(info_random, decoded_random[:64]) and success_random:
        print("   ✓ 随机信息位测试通过")
    else:
        print("   ✗ 随机信息位测试失败")
    
    # 4. 校验性质测试
    print("\n4. 校验性质测试...")
    syndrome = np.dot(H, codeword_random) % 2
    if np.sum(syndrome) == 0:
        print("   ✓ 校验性质测试通过")
    else:
        print("   ✗ 校验性质测试失败")
    
    # 5. 噪声环境测试
    print("\n5. 噪声环境测试...")
    
    # AWGN信道测试
    snr_db = 3.0
    received_signal, llr_values = ChannelSimulator.awgn_channel(codeword_random, snr_db)
    decoded_awgn, success_awgn = decoder.soft_decode(llr_values)
    
    bit_errors_awgn = np.sum(info_random != decoded_awgn[:64])
    print(f"   AWGN信道(SNR={snr_db}dB): {bit_errors_awgn}/64 比特错误, 解码{'成功' if success_awgn else '失败'}")
    
    # BSC信道测试
    error_prob = 0.03
    received_bsc = ChannelSimulator.bsc_channel(codeword_random, error_prob)
    decoded_bsc, success_bsc = decoder.hard_decode(received_bsc)
    
    bit_errors_bsc = np.sum(info_random != decoded_bsc[:64])
    print(f"   BSC信道(p={error_prob}): {bit_errors_bsc}/64 比特错误, 解码{'成功' if success_bsc else '失败'}")
    
    # 6. 性能统计
    print("\n6. 简单性能统计...")
    
    total_tests = 50
    awgn_success_count = 0
    bsc_success_count = 0
    awgn_total_errors = 0
    bsc_total_errors = 0
    
    for i in range(total_tests):
        # 生成随机信息位
        info_bits = np.random.randint(0, 2, 64)
        codeword = encoder.encode(info_bits)
        
        # AWGN测试
        _, llr_vals = ChannelSimulator.awgn_channel(codeword, 2.0)
        decoded_awgn, succ_awgn = decoder.soft_decode(llr_vals)
        if succ_awgn:
            awgn_success_count += 1
        awgn_total_errors += np.sum(info_bits != decoded_awgn[:64])
        
        # BSC测试
        recv_bsc = ChannelSimulator.bsc_channel(codeword, 0.05)
        decoded_bsc, succ_bsc = decoder.hard_decode(recv_bsc)
        if succ_bsc:
            bsc_success_count += 1
        bsc_total_errors += np.sum(info_bits != decoded_bsc[:64])
    
    awgn_success_rate = awgn_success_count / total_tests
    bsc_success_rate = bsc_success_count / total_tests
    awgn_ber = awgn_total_errors / (total_tests * 64)
    bsc_ber = bsc_total_errors / (total_tests * 64)
    
    print(f"   AWGN信道(SNR=2dB): 成功率 {awgn_success_rate:.1%}, BER {awgn_ber:.2e}")
    print(f"   BSC信道(p=0.05): 成功率 {bsc_success_rate:.1%}, BER {bsc_ber:.2e}")
    
    # 7. 矩阵属性检查
    print("\n7. 矩阵属性检查...")
    
    # 检查校验矩阵的稀疏性
    total_elements = H.shape[0] * H.shape[1]
    nonzero_elements = np.sum(H)
    sparsity = 1 - (nonzero_elements / total_elements)
    print(f"   校验矩阵稀疏度: {sparsity:.3f}")
    
    # 检查行重和列重
    row_weights = np.sum(H, axis=1)
    col_weights = np.sum(H, axis=0)
    print(f"   行重: 最小={np.min(row_weights)}, 最大={np.max(row_weights)}, 平均={np.mean(row_weights):.1f}")
    print(f"   列重: 最小={np.min(col_weights)}, 最大={np.max(col_weights)}, 平均={np.mean(col_weights):.1f}")
    
    # 8. QC结构验证
    print("\n8. QC结构验证...")
    
    # 检查子矩阵结构
    sub_size = 16
    is_qc_structure = True
    
    for i in range(4):  # 4行子矩阵
        for j in range(8):  # 8列子矩阵
            sub_matrix = H[i*sub_size:(i+1)*sub_size, j*sub_size:(j+1)*sub_size]
            
            # 检查是否为循环矩阵或零矩阵
            if np.sum(sub_matrix) == 0:
                continue  # 零矩阵
            elif np.sum(sub_matrix) == sub_size:
                # 检查是否为循环置换矩阵
                row_sums = np.sum(sub_matrix, axis=1)
                col_sums = np.sum(sub_matrix, axis=0)
                if not (np.all(row_sums == 1) and np.all(col_sums == 1)):
                    is_qc_structure = False
                    break
            else:
                is_qc_structure = False
                break
        if not is_qc_structure:
            break
    
    if is_qc_structure:
        print("   ✓ QC结构验证通过")
    else:
        print("   ✗ QC结构验证失败")
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("=" * 50)

if __name__ == "__main__":
    # 设置随机种子以获得可重复的结果
    np.random.seed(42)
    
    # 运行测试
    simple_test()
