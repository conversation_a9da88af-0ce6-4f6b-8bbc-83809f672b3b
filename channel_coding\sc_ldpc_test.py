"""
SC-LDPC (Spatially Coupled LDPC) 编解码器测试

本脚本用于测试SC-LDPC码的各项功能，包括：
- 构造器测试
- 编码器测试
- 解码器测试（硬解码、软解码、滑窗解码）
- 性能测试

作者：AI Assistant
日期：2024
"""

import numpy as np
import sys
import traceback
from ldpc import *

def test_sc_ldpc_constructor():
    """测试SC-LDPC构造器"""
    print("=== 测试SC-LDPC构造器 ===")
    
    # 测试1：基本构造
    print("测试1: 基本构造")
    base_matrix = np.array([
        [1, 1, 0],
        [0, 1, 1]
    ])
    
    try:
        constructor = SCLDPCConstructor(base_matrix, coupling_length=3, memory_length=1)
        H_coupled = constructor.construct_coupled_matrix()
        params = constructor.get_code_parameters()
        
        print(f"  基础矩阵: {base_matrix.shape}")
        print(f"  耦合矩阵: {H_coupled.shape}")
        print(f"  码率: {params['rate']:.3f}")
        print("  ✓ 基本构造测试通过")
    except Exception as e:
        print(f"  ✗ 基本构造测试失败: {e}")
    
    # 测试2：参数验证
    print("\n测试2: 参数验证")
    try:
        # 测试无效参数
        try:
            SCLDPCConstructor(base_matrix, coupling_length=0)
            print("  ✗ 应该拒绝无效的耦合长度")
        except ValueError:
            print("  ✓ 正确拒绝无效的耦合长度")
            
        try:
            SCLDPCConstructor(base_matrix, coupling_length=3, memory_length=0)
            print("  ✗ 应该拒绝无效的记忆长度")
        except ValueError:
            print("  ✓ 正确拒绝无效的记忆长度")
            
    except Exception as e:
        print(f"  ✗ 参数验证测试失败: {e}")
    
    print()

def test_sc_ldpc_encoder():
    """测试SC-LDPC编码器"""
    print("=== 测试SC-LDPC编码器 ===")
    
    # 创建测试用的SC-LDPC码
    base_matrix = np.array([
        [1, 1],
        [1, 0]
    ])
    
    constructor = SCLDPCConstructor(base_matrix, coupling_length=3, memory_length=1)
    H_coupled = constructor.construct_coupled_matrix()
    
    print(f"测试矩阵大小: {H_coupled.shape}")
    
    # 测试1：基本编码
    print("测试1: 基本编码")
    try:
        encoder = SCLDPCEncoder(H_coupled)
        params = encoder.get_code_parameters()
        
        if params['k'] > 0:
            info_bits = np.random.randint(0, 2, params['k'])
            codeword = encoder.encode(info_bits)
            
            print(f"  信息位长度: {len(info_bits)}")
            print(f"  码字长度: {len(codeword)}")
            print(f"  码率: {params['rate']:.3f}")
            print("  ✓ 基本编码测试通过")
        else:
            print("  ⚠ 信息位数为0，跳过编码测试")
            
    except Exception as e:
        print(f"  ✗ 基本编码测试失败: {e}")
    
    # 测试2：编码一致性
    print("\n测试2: 编码一致性")
    try:
        if params['k'] > 0:
            # 相同输入应产生相同输出
            info_bits = np.array([1, 0] * (params['k'] // 2) + [1] * (params['k'] % 2))[:params['k']]
            codeword1 = encoder.encode(info_bits)
            codeword2 = encoder.encode(info_bits)
            
            if np.array_equal(codeword1, codeword2):
                print("  ✓ 编码一致性测试通过")
            else:
                print("  ✗ 编码一致性测试失败")
        else:
            print("  ⚠ 信息位数为0，跳过一致性测试")
            
    except Exception as e:
        print(f"  ✗ 编码一致性测试失败: {e}")
    
    print()

def test_sc_ldpc_decoder():
    """测试SC-LDPC解码器"""
    print("=== 测试SC-LDPC解码器 ===")
    
    # 创建测试用的SC-LDPC码
    examples = create_example_sc_ldpc_codes()
    
    if not examples:
        print("无法创建测试用SC-LDPC码")
        return
        
    example_name = list(examples.keys())[0]
    example = examples[example_name]
    H_coupled = example['H']
    
    print(f"使用示例: {example['description']}")
    print(f"矩阵大小: {H_coupled.shape}")
    
    # 创建编解码器
    encoder = SCLDPCEncoder(H_coupled)
    decoder = SCLDPCDecoder(H_coupled)
    
    params = encoder.get_code_parameters()
    
    if params['k'] <= 0:
        print("信息位数为0，跳过解码测试")
        return
    
    # 生成测试数据
    info_bits = np.random.randint(0, 2, params['k'])
    codeword = encoder.encode(info_bits)
    
    # 测试1：无噪声软解码
    print("\n测试1: 无噪声软解码")
    try:
        symbols = bits_to_bpsk(codeword)
        llr = bpsk_to_llr(symbols, 0.001)  # 很小的噪声
        
        decoded_bits, converged, iterations = decoder.decode_soft(llr)
        
        if len(decoded_bits) >= len(codeword):
            ber = calculate_ber(codeword, decoded_bits[:len(codeword)])
        else:
            ber = 1.0  # 如果长度不匹配，认为完全错误
            
        print(f"  收敛: {converged}")
        print(f"  迭代次数: {iterations}")
        print(f"  误码率: {ber:.4f}")
        
        if ber < 0.1:  # 允许少量错误
            print("  ✓ 无噪声软解码测试通过")
        else:
            print("  ✗ 无噪声软解码测试失败")
            
    except Exception as e:
        print(f"  ✗ 无噪声软解码测试失败: {e}")
    
    # 测试2：硬解码
    print("\n测试2: 硬解码")
    try:
        # 添加少量错误
        received_hard = codeword.copy()
        if len(received_hard) > 2:
            error_pos = np.random.choice(len(received_hard), size=1, replace=False)
            received_hard[error_pos] = 1 - received_hard[error_pos]
        
        decoded_hard, converged_hard, iter_hard = decoder.decode_hard(received_hard)
        
        print(f"  收敛: {converged_hard}")
        print(f"  迭代次数: {iter_hard}")
        print("  ✓ 硬解码测试通过")
        
    except Exception as e:
        print(f"  ✗ 硬解码测试失败: {e}")
    
    # 测试3：滑窗解码（如果码长足够）
    if len(codeword) > 10:
        print("\n测试3: 滑窗解码")
        try:
            window_size = min(len(codeword) // 2, 20)
            decoded_sliding, avg_iter = decoder.decode_sliding_window(llr, window_size, overlap=2)
            
            print(f"  窗口大小: {window_size}")
            print(f"  平均迭代次数: {avg_iter:.1f}")
            print("  ✓ 滑窗解码测试通过")
            
        except Exception as e:
            print(f"  ✗ 滑窗解码测试失败: {e}")
    
    print()

def test_sc_ldpc_performance():
    """测试SC-LDPC性能仿真"""
    print("=== 测试SC-LDPC性能仿真 ===")
    
    # 创建测试用的SC-LDPC码
    examples = create_example_sc_ldpc_codes()
    
    if not examples:
        print("无法创建测试用SC-LDPC码")
        return
        
    example_name = list(examples.keys())[0]
    example = examples[example_name]
    H_coupled = example['H']
    
    print(f"使用示例: {example['description']}")
    
    # 测试1：基本性能仿真
    print("\n测试1: 基本性能仿真")
    try:
        snr_range = [2, 4]  # 简化测试
        num_frames = 10     # 减少帧数
        
        snr_list, ber_list, fer_list = sc_ldpc_simulation(
            H_coupled, snr_range, num_frames, decode_method='soft', verbose=False
        )
        
        print(f"  SNR范围: {snr_range}")
        print(f"  BER结果: {[f'{ber:.2e}' for ber in ber_list]}")
        print(f"  FER结果: {[f'{fer:.2e}' for fer in fer_list]}")
        print("  ✓ 基本性能仿真测试通过")
        
    except Exception as e:
        print(f"  ✗ 基本性能仿真测试失败: {e}")
    
    # 测试2：不同解码方法比较
    print("\n测试2: 不同解码方法比较")
    try:
        methods = ['soft', 'hard']
        snr_test = 3
        num_frames = 5
        
        for method in methods:
            snr_list, ber_list, fer_list = sc_ldpc_simulation(
                H_coupled, [snr_test], num_frames, decode_method=method, verbose=False
            )
            print(f"  {method}解码 - BER: {ber_list[0]:.2e}, FER: {fer_list[0]:.2e}")
            
        print("  ✓ 解码方法比较测试通过")
        
    except Exception as e:
        print(f"  ✗ 解码方法比较测试失败: {e}")
    
    print()

def run_all_tests():
    """运行所有测试"""
    print("SC-LDPC编解码器测试套件")
    print("=" * 50)
    
    tests = [
        test_sc_ldpc_constructor,
        test_sc_ldpc_encoder,
        test_sc_ldpc_decoder,
        test_sc_ldpc_performance
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            test_func()
            passed += 1
        except Exception as e:
            print(f"测试 {test_func.__name__} 出现异常: {e}")
            traceback.print_exc()
            print()
    
    print("=" * 50)
    print(f"测试完成: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("✓ 所有测试通过！")
        return True
    else:
        print("✗ 部分测试失败")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
