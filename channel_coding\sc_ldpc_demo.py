"""
SC-LDPC (Spatially Coupled LDPC) 编解码器演示

本脚本演示SC-LDPC码的构造、编码和解码功能，包括：
- SC-LDPC码的构造方法
- 硬解码和软解码
- 滑窗解码
- 性能比较和仿真

作者：AI Assistant
日期：2024
"""

import numpy as np
import matplotlib.pyplot as plt
from ldpc import *

def demo_sc_ldpc_construction():
    """演示SC-LDPC码的构造"""
    print("=== SC-LDPC码构造演示 ===\n")
    
    # 定义基础原型矩阵
    base_matrix = np.array([
        [1, 1, 0],
        [0, 1, 1]
    ])
    
    print("基础原型矩阵:")
    print(base_matrix)
    print()
    
    # 构造SC-LDPC码
    coupling_length = 4
    memory_length = 2
    
    constructor = SCLDPCConstructor(base_matrix, coupling_length, memory_length)
    H_coupled = constructor.construct_coupled_matrix()
    
    print(f"SC-LDPC参数:")
    params = constructor.get_code_parameters()
    for key, value in params.items():
        print(f"  {key}: {value}")
    print()
    
    print(f"耦合校验矩阵 ({H_coupled.shape[0]} x {H_coupled.shape[1]}):")
    print(H_coupled)
    print()
    
    return H_coupled

def demo_sc_ldpc_encoding():
    """演示SC-LDPC编码"""
    print("=== SC-LDPC编码演示 ===\n")
    
    # 使用示例SC-LDPC码
    examples = create_example_sc_ldpc_codes()
    
    if not examples:
        print("无法创建SC-LDPC示例码")
        return None, None
        
    example_name = list(examples.keys())[0]
    example = examples[example_name]
    H_coupled = example['H']
    
    print(f"使用示例: {example['description']}")
    print(f"矩阵大小: {H_coupled.shape}")
    print()
    
    # 创建编码器
    encoder = SCLDPCEncoder(H_coupled)
    params = encoder.get_code_parameters()
    
    print("编码器参数:")
    for key, value in params.items():
        print(f"  {key}: {value}")
    print()
    
    # 生成随机信息位
    k = params['k']
    if k > 0:
        info_bits = np.random.randint(0, 2, k)
        print(f"信息位 ({len(info_bits)}): {info_bits}")
        
        # 编码
        codeword = encoder.encode(info_bits)
        print(f"码字 ({len(codeword)}): {codeword}")
        print()
        
        return H_coupled, codeword
    else:
        print("警告：信息位数为0")
        return H_coupled, None

def demo_sc_ldpc_decoding():
    """演示SC-LDPC解码"""
    print("=== SC-LDPC解码演示 ===\n")
    
    # 获取编码结果
    H_coupled, codeword = demo_sc_ldpc_encoding()
    
    if codeword is None:
        print("无法获取有效的码字")
        return
        
    # 创建解码器
    decoder = SCLDPCDecoder(H_coupled)
    
    # 1. 软解码测试
    print("1. 软解码测试:")
    symbols = bits_to_bpsk(codeword)
    
    # 添加噪声
    snr_db = 2
    received_signal, noise_var = awgn_channel(symbols, snr_db)
    llr = bpsk_to_llr(received_signal, noise_var)
    
    print(f"  SNR: {snr_db} dB")
    print(f"  原始码字: {codeword}")
    
    decoded_bits, converged, iterations = decoder.decode_soft(llr, verbose=False)
    print(f"  软解码结果: {decoded_bits}")
    print(f"  收敛: {converged}, 迭代次数: {iterations}")
    
    # 计算误码率
    ber = calculate_ber(codeword, decoded_bits)
    print(f"  误码率: {ber:.4f}")
    print()
    
    # 2. 硬解码测试
    print("2. 硬解码测试:")
    # 人工添加一些错误
    received_hard = codeword.copy()
    error_positions = np.random.choice(len(codeword), size=min(2, len(codeword)//4), replace=False)
    received_hard[error_positions] = 1 - received_hard[error_positions]
    
    print(f"  接收硬比特: {received_hard}")
    print(f"  错误位置: {error_positions}")
    
    decoded_hard, converged_hard, iter_hard = decoder.decode_hard(received_hard, verbose=False)
    print(f"  硬解码结果: {decoded_hard}")
    print(f"  收敛: {converged_hard}, 迭代次数: {iter_hard}")
    
    ber_hard = calculate_ber(codeword, decoded_hard)
    print(f"  误码率: {ber_hard:.4f}")
    print()
    
    # 3. 滑窗解码测试（如果码长足够）
    if len(codeword) > 20:
        print("3. 滑窗解码测试:")
        window_size = min(len(codeword) // 2, 30)
        overlap = 5
        
        print(f"  窗口大小: {window_size}, 重叠: {overlap}")
        
        decoded_sliding, avg_iter = decoder.decode_sliding_window(llr, window_size, overlap, verbose=False)
        print(f"  滑窗解码结果: {decoded_sliding}")
        print(f"  平均迭代次数: {avg_iter:.1f}")
        
        ber_sliding = calculate_ber(codeword, decoded_sliding)
        print(f"  误码率: {ber_sliding:.4f}")

def demo_sc_ldpc_performance():
    """演示SC-LDPC性能仿真"""
    print("=== SC-LDPC性能仿真演示 ===\n")
    
    # 创建示例SC-LDPC码
    examples = create_example_sc_ldpc_codes()
    
    if not examples:
        print("无法创建SC-LDPC示例码")
        return
        
    example_name = list(examples.keys())[0]
    example = examples[example_name]
    H_coupled = example['H']
    
    print(f"使用示例: {example['description']}")
    print(f"矩阵大小: {H_coupled.shape}")
    print()
    
    # 仿真参数
    snr_range = [0, 1, 2, 3]
    num_frames = 20  # 减少帧数以加快演示
    
    print(f"仿真参数:")
    print(f"  SNR范围: {snr_range} dB")
    print(f"  仿真帧数: {num_frames}")
    print()
    
    # 比较不同解码方法
    methods = ['soft', 'hard']
    results = {}
    
    for method in methods:
        print(f"测试解码方法: {method}")
        try:
            snr_list, ber_list, fer_list = sc_ldpc_simulation(
                H_coupled, snr_range, num_frames, decode_method=method, verbose=False
            )
            results[method] = {
                'snr': snr_list,
                'ber': ber_list,
                'fer': fer_list
            }
            print(f"  完成")
        except Exception as e:
            print(f"  失败: {e}")
    
    # 显示结果
    print("\n性能比较结果:")
    print("SNR(dB) | 方法 | BER | FER")
    print("-" * 35)
    
    for snr in snr_range:
        for method in results:
            if snr in results[method]['snr']:
                idx = results[method]['snr'].index(snr)
                ber = results[method]['ber'][idx]
                fer = results[method]['fer'][idx]
                print(f"{snr:7.1f} | {method:>4} | {ber:.2e} | {fer:.2e}")
        if len(results) > 1:
            print("-" * 35)

def main():
    """主演示函数"""
    print("SC-LDPC (Spatially Coupled LDPC) 编解码器演示\n")
    print("=" * 50)
    
    try:
        # 1. 构造演示
        demo_sc_ldpc_construction()
        print("\n" + "=" * 50)
        
        # 2. 解码演示
        demo_sc_ldpc_decoding()
        print("\n" + "=" * 50)
        
        # 3. 性能演示
        demo_sc_ldpc_performance()
        print("\n" + "=" * 50)
        
        print("\n演示完成！")
        
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
