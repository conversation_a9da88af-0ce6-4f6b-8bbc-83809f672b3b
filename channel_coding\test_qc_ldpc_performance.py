"""
CCSDS (128,64) QC-LDPC码性能测试脚本

本脚本提供更详细的性能测试，包括：
1. 编码器性能验证
2. 解码器性能比较（硬解码 vs 软解码）
3. 不同信道条件下的BER性能
4. 解码收敛性分析
5. 计算复杂度分析

作者：AI Assistant
日期：2024
"""

import numpy as np
import matplotlib.pyplot as plt
import time
from qc_ldpc_ccsds import QCLDPCConstructor, QCLDPCEncoder, QCLDPCDecoder, ChannelSimulator, BERSimulator

def test_encoder_properties():
    """
    测试编码器的基本性质
    """
    print("=" * 50)
    print("编码器性质测试")
    print("=" * 50)
    
    # 构造码
    constructor = QCLDPCConstructor()
    H = constructor.construct_ccsds_128_64_matrix()
    encoder = QCLDPCEncoder(H)
    
    print(f"校验矩阵H的维度: {H.shape}")
    print(f"生成矩阵G的维度: {encoder.G.shape}")
    
    # 测试线性性质
    print("\n测试编码器线性性质...")
    u1 = np.random.randint(0, 2, encoder.k)
    u2 = np.random.randint(0, 2, encoder.k)
    u3 = (u1 + u2) % 2
    
    c1 = encoder.encode(u1)
    c2 = encoder.encode(u2)
    c3 = encoder.encode(u3)
    c_sum = (c1 + c2) % 2
    
    if np.array_equal(c3, c_sum):
        print("✓ 线性性质验证通过")
    else:
        print("✗ 线性性质验证失败")
    
    # 测试系统性质
    print("\n测试系统码性质...")
    test_info = np.random.randint(0, 2, encoder.k)
    codeword = encoder.encode(test_info)
    
    if np.array_equal(test_info, codeword[:encoder.k]):
        print("✓ 系统码性质验证通过")
    else:
        print("✗ 系统码性质验证失败")
    
    # 测试校验性质
    print("\n测试校验性质...")
    num_tests = 100
    all_valid = True
    
    for _ in range(num_tests):
        info_bits = np.random.randint(0, 2, encoder.k)
        codeword = encoder.encode(info_bits)
        syndrome = np.dot(H, codeword) % 2
        
        if np.sum(syndrome) != 0:
            all_valid = False
            break
    
    if all_valid:
        print(f"✓ 校验性质验证通过 ({num_tests}次测试)")
    else:
        print("✗ 校验性质验证失败")


def test_decoder_convergence():
    """
    测试解码器收敛性
    """
    print("\n" + "=" * 50)
    print("解码器收敛性测试")
    print("=" * 50)
    
    constructor = QCLDPCConstructor()
    H = constructor.construct_ccsds_128_64_matrix()
    encoder = QCLDPCEncoder(H)
    decoder = QCLDPCDecoder(H, max_iterations=100)
    
    # 测试不同SNR下的收敛性
    snr_values = [0, 1, 2, 3, 4, 5]
    convergence_rates = []
    
    for snr in snr_values:
        print(f"\n测试SNR = {snr} dB...")
        converged_count = 0
        total_tests = 50
        
        for _ in range(total_tests):
            # 生成随机码字
            info_bits = np.random.randint(0, 2, encoder.k)
            codeword = encoder.encode(info_bits)
            
            # 添加噪声
            _, llr_values = ChannelSimulator.awgn_channel(codeword, snr)
            
            # 解码
            _, success = decoder.soft_decode(llr_values)
            if success:
                converged_count += 1
        
        convergence_rate = converged_count / total_tests
        convergence_rates.append(convergence_rate)
        print(f"  收敛率: {convergence_rate:.2%}")
    
    # 绘制收敛率曲线
    plt.figure(figsize=(10, 6))
    plt.plot(snr_values, convergence_rates, 'b-o', linewidth=2, markersize=6)
    plt.grid(True, alpha=0.3)
    plt.xlabel('SNR (dB)')
    plt.ylabel('Convergence Rate')
    plt.title('LDPC Decoder Convergence Rate vs SNR')
    plt.ylim([0, 1.1])
    plt.tight_layout()
    plt.show()


def compare_hard_soft_decoding():
    """
    比较硬解码和软解码性能
    """
    print("\n" + "=" * 50)
    print("硬解码 vs 软解码性能比较")
    print("=" * 50)
    
    constructor = QCLDPCConstructor()
    H = constructor.construct_ccsds_128_64_matrix()
    encoder = QCLDPCEncoder(H)
    decoder = QCLDPCDecoder(H, max_iterations=50)
    
    # 测试不同错误概率下的性能
    error_probs = np.linspace(0.01, 0.1, 5)
    hard_ber = []
    soft_ber = []
    
    for p in error_probs:
        print(f"\n测试错误概率 p = {p:.3f}...")
        
        # 硬解码测试
        total_bits_hard = 0
        error_bits_hard = 0
        
        # 软解码测试（将BSC转换为等效的AWGN）
        total_bits_soft = 0
        error_bits_soft = 0
        
        num_tests = 100
        
        for _ in range(num_tests):
            info_bits = np.random.randint(0, 2, encoder.k)
            codeword = encoder.encode(info_bits)
            
            # 硬解码测试
            received_hard = ChannelSimulator.bsc_channel(codeword, p)
            decoded_hard, _ = decoder.hard_decode(received_hard)
            error_bits_hard += np.sum(info_bits != decoded_hard[:encoder.k])
            total_bits_hard += encoder.k
            
            # 软解码测试（模拟等效SNR）
            # 对于BSC，等效SNR约为 -10*log10(4*p*(1-p))
            equiv_snr = -10 * np.log10(4 * p * (1 - p)) if p < 0.5 else 0
            _, llr_values = ChannelSimulator.awgn_channel(codeword, equiv_snr)
            decoded_soft, _ = decoder.soft_decode(llr_values)
            error_bits_soft += np.sum(info_bits != decoded_soft[:encoder.k])
            total_bits_soft += encoder.k
        
        ber_hard = error_bits_hard / total_bits_hard
        ber_soft = error_bits_soft / total_bits_soft
        
        hard_ber.append(ber_hard)
        soft_ber.append(ber_soft)
        
        print(f"  硬解码BER: {ber_hard:.2e}")
        print(f"  软解码BER: {ber_soft:.2e}")
    
    # 绘制比较图
    plt.figure(figsize=(10, 6))
    plt.semilogy(error_probs, hard_ber, 'r-o', label='Hard Decoding', linewidth=2)
    plt.semilogy(error_probs, soft_ber, 'b-s', label='Soft Decoding', linewidth=2)
    plt.grid(True, which="both", ls="-", alpha=0.3)
    plt.xlabel('Channel Error Probability')
    plt.ylabel('Bit Error Rate (BER)')
    plt.title('Hard vs Soft Decoding Performance')
    plt.legend()
    plt.tight_layout()
    plt.show()


def test_computational_complexity():
    """
    测试计算复杂度
    """
    print("\n" + "=" * 50)
    print("计算复杂度测试")
    print("=" * 50)
    
    constructor = QCLDPCConstructor()
    H = constructor.construct_ccsds_128_64_matrix()
    encoder = QCLDPCEncoder(H)
    decoder = QCLDPCDecoder(H, max_iterations=50)
    
    # 编码时间测试
    print("\n编码时间测试...")
    num_encodings = 1000
    info_bits = np.random.randint(0, 2, encoder.k)
    
    start_time = time.time()
    for _ in range(num_encodings):
        _ = encoder.encode(info_bits)
    encoding_time = time.time() - start_time
    
    print(f"编码 {num_encodings} 次耗时: {encoding_time:.3f} 秒")
    print(f"平均编码时间: {encoding_time/num_encodings*1000:.3f} 毫秒")
    
    # 解码时间测试
    print("\n解码时间测试...")
    num_decodings = 100
    codeword = encoder.encode(info_bits)
    _, llr_values = ChannelSimulator.awgn_channel(codeword, 2.0)
    
    start_time = time.time()
    for _ in range(num_decodings):
        _ = decoder.soft_decode(llr_values)
    decoding_time = time.time() - start_time
    
    print(f"解码 {num_decodings} 次耗时: {decoding_time:.3f} 秒")
    print(f"平均解码时间: {decoding_time/num_decodings*1000:.3f} 毫秒")


def main():
    """
    主测试函数
    """
    print("CCSDS (128,64) QC-LDPC码详细性能测试")
    print("=" * 60)
    
    # 设置随机种子
    np.random.seed(42)
    
    # 运行各项测试
    test_encoder_properties()
    test_decoder_convergence()
    compare_hard_soft_decoding()
    test_computational_complexity()
    
    print("\n" + "=" * 60)
    print("所有测试完成！")
    print("=" * 60)


if __name__ == "__main__":
    main()
