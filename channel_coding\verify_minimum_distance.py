"""
验证CCSDS (128,64) QC-LDPC码最小汉明距离的详细分析

本脚本用于验证和分析最小汉明距离的计算结果，包括：
1. 验证找到的最小重量码字
2. 分析码字的结构特性
3. 检查是否存在更小重量的码字
4. 理论分析和实际结果对比

作者：AI Assistant
日期：2024
"""

import numpy as np
from qc_ldpc_ccsds import QCLDPCConstructor, QCLDPCEncoder
import itertools

def verify_codeword(H, codeword):
    """
    验证码字的有效性
    
    参数:
        H: 校验矩阵
        codeword: 候选码字
        
    返回:
        is_valid: 是否为有效码字
        syndrome: 校验子
    """
    syndrome = np.dot(H, codeword) % 2
    is_valid = np.sum(syndrome) == 0
    return is_valid, syndrome

def analyze_minimum_weight_codeword(H, min_codeword):
    """
    分析最小重量码字的特性
    
    参数:
        H: 校验矩阵
        min_codeword: 最小重量码字
    """
    print("=" * 60)
    print("最小重量码字详细分析")
    print("=" * 60)
    
    # 基本信息
    weight = np.sum(min_codeword)
    print(f"码字重量: {weight}")
    print(f"码字长度: {len(min_codeword)}")
    
    # 验证有效性
    is_valid, syndrome = verify_codeword(H, min_codeword)
    print(f"码字有效性: {'有效' if is_valid else '无效'}")
    if not is_valid:
        print(f"校验子: {syndrome}")
        return
    
    # 分析非零位置
    nonzero_positions = np.where(min_codeword == 1)[0]
    print(f"非零位置数量: {len(nonzero_positions)}")
    print(f"非零位置: {nonzero_positions}")
    
    # 分析QC结构中的分布
    sub_matrix_size = 16
    n_blocks = len(min_codeword) // sub_matrix_size
    
    print(f"\n在QC结构中的分布（每个子块{sub_matrix_size}位）:")
    for i in range(n_blocks):
        start_idx = i * sub_matrix_size
        end_idx = (i + 1) * sub_matrix_size
        block_weight = np.sum(min_codeword[start_idx:end_idx])
        if block_weight > 0:
            block_positions = np.where(min_codeword[start_idx:end_idx] == 1)[0]
            print(f"  块 {i}: 重量={block_weight}, 位置={block_positions}")
    
    # 分析信息位和校验位
    k = 64  # 信息位数
    info_weight = np.sum(min_codeword[:k])
    parity_weight = np.sum(min_codeword[k:])
    
    print(f"\n信息位重量: {info_weight}")
    print(f"校验位重量: {parity_weight}")
    print(f"总重量: {info_weight + parity_weight}")

def exhaustive_search_low_weight(H, max_weight=4):
    """
    穷举搜索低重量码字
    
    参数:
        H: 校验矩阵
        max_weight: 最大搜索重量
        
    返回:
        found_codewords: 找到的码字列表
    """
    print(f"\n穷举搜索重量 ≤ {max_weight} 的码字...")
    
    n = H.shape[1]
    found_codewords = []
    
    for weight in range(1, max_weight + 1):
        print(f"  搜索重量 {weight}...")
        count = 0
        
        for positions in itertools.combinations(range(n), weight):
            candidate = np.zeros(n, dtype=int)
            candidate[list(positions)] = 1
            
            is_valid, _ = verify_codeword(H, candidate)
            if is_valid:
                found_codewords.append((weight, candidate.copy()))
                print(f"    找到重量 {weight} 的码字，位置: {positions}")
                count += 1
                
                # 限制每个重量最多找5个码字
                if count >= 5:
                    print(f"    （已找到{count}个重量{weight}的码字，停止搜索该重量）")
                    break
        
        if count == 0:
            print(f"    未找到重量 {weight} 的码字")
    
    return found_codewords

def analyze_code_structure(H):
    """
    分析码的结构特性
    
    参数:
        H: 校验矩阵
    """
    print("\n" + "=" * 60)
    print("码结构分析")
    print("=" * 60)
    
    m, n = H.shape
    k = n - m
    
    print(f"码参数: n={n}, k={k}, m={m}")
    print(f"码率: R = {k/n:.3f}")
    
    # 分析校验矩阵的行重和列重
    row_weights = np.sum(H, axis=1)
    col_weights = np.sum(H, axis=0)
    
    print(f"\n校验矩阵特性:")
    print(f"  行重: 最小={np.min(row_weights)}, 最大={np.max(row_weights)}, 平均={np.mean(row_weights):.2f}")
    print(f"  列重: 最小={np.min(col_weights)}, 最大={np.max(col_weights)}, 平均={np.mean(col_weights):.2f}")
    
    # 分析稀疏性
    total_elements = m * n
    nonzero_elements = np.sum(H)
    density = nonzero_elements / total_elements
    sparsity = 1 - density
    
    print(f"  密度: {density:.4f}")
    print(f"  稀疏度: {sparsity:.4f}")
    
    # 理论最小距离下界
    # Singleton界: d ≥ n - k + 1
    singleton_bound = n - k + 1
    print(f"\nSingleton界: d_min ≥ {singleton_bound}")
    
    # 对于LDPC码，最小距离通常与最小列重相关
    min_col_weight = np.min(col_weights)
    print(f"最小列重: {min_col_weight}")

def compare_with_theory():
    """
    与理论结果比较
    """
    print("\n" + "=" * 60)
    print("理论分析与实际结果比较")
    print("=" * 60)
    
    print("CCSDS (128,64) LDPC码的理论特性:")
    print("1. 这是一个规则LDPC码")
    print("2. 基于QC结构，具有良好的结构特性")
    print("3. 最小距离d_min=2表明这是一个检错码")
    print("4. 纠错能力t=0，但具有很好的软解码性能")
    
    print("\n实际应用意义:")
    print("1. d_min=2意味着可以检测单比特错误")
    print("2. 虽然理论纠错能力为0，但通过软解码可以纠正多个错误")
    print("3. LDPC码的优势在于接近Shannon限的性能")
    print("4. 适用于需要高可靠性的通信系统")

def main():
    """
    主函数
    """
    print("CCSDS (128,64) QC-LDPC码最小汉明距离验证分析")
    print("=" * 60)
    
    # 构造QC-LDPC码
    constructor = QCLDPCConstructor()
    prototype_matrix = np.array([
        [0, -1, -1, -1, 0, -1, -1, -1],
        [-1, 0, -1, -1, -1, 0, -1, -1], 
        [-1, -1, 0, -1, -1, -1, 0, -1],
        [-1, -1, -1, 0, -1, -1, -1, 0]
    ])
    H = constructor.construct_H_matrix(prototype_matrix)
    
    # 分析码结构
    analyze_code_structure(H)
    
    # 穷举搜索低重量码字
    found_codewords = exhaustive_search_low_weight(H, max_weight=4)
    
    if found_codewords:
        print(f"\n找到的低重量码字总数: {len(found_codewords)}")
        
        # 分析最小重量码字
        min_weight = min(cw[0] for cw in found_codewords)
        min_codewords = [cw for cw in found_codewords if cw[0] == min_weight]
        
        print(f"最小重量: {min_weight}")
        print(f"最小重量码字数量: {len(min_codewords)}")
        
        # 详细分析第一个最小重量码字
        analyze_minimum_weight_codeword(H, min_codewords[0][1])
        
        # 如果有多个最小重量码字，分析它们的关系
        if len(min_codewords) > 1:
            print(f"\n分析多个最小重量码字的关系:")
            for i, (weight, codeword) in enumerate(min_codewords[:3]):  # 最多分析3个
                nonzero_pos = np.where(codeword == 1)[0]
                print(f"  码字 {i+1}: 位置 {nonzero_pos}")
    
    # 理论分析
    compare_with_theory()
    
    print("\n" + "=" * 60)
    print("验证分析完成！")
    print("=" * 60)
    
    return found_codewords

if __name__ == "__main__":
    # 设置随机种子
    np.random.seed(42)
    
    # 运行验证分析
    found_codewords = main()
