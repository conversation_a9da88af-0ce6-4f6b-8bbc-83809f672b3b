"""
LDPC (Low-Density Parity-Check) 编解码器实现

本模块实现了LDPC码的编码和解码功能，包括：
- LDPC编码器：基于校验矩阵的系统编码
- LDPC解码器：基于置信传播算法的软判决解码
- 校验矩阵生成：支持规则和不规则LDPC码
- 性能评估：误码率测试和收敛性分析

作者：AI Assistant
日期：2024
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy.sparse import csr_matrix
from scipy.sparse.linalg import spsolve
import warnings
warnings.filterwarnings('ignore')


class LDPCEncoder:
    """
    LDPC编码器类

    实现基于校验矩阵的LDPC编码功能，支持系统码和非系统码
    """

    def __init__(self, H, systematic=True):
        """
        初始化LDPC编码器

        参数:
            H: 校验矩阵 (m x n)，其中 m 是校验位数，n 是码字长度
            systematic: 是否使用系统编码，默认为True
        """
        self.H = np.array(H, dtype=int)
        self.m, self.n = self.H.shape  # m: 校验位数, n: 码字长度
        self.k = self.n - self.m       # k: 信息位数
        self.systematic = systematic

        if self.k <= 0:
            raise ValueError("校验矩阵维度错误：信息位数必须大于0")

        # 验证校验矩阵
        if not self._validate_parity_matrix():
            warnings.warn("校验矩阵可能不满足LDPC码的要求")

        # 生成生成矩阵
        self.G = self._generate_generator_matrix()

    def _validate_parity_matrix(self):
        """验证校验矩阵是否满足LDPC码的基本要求"""
        # 检查矩阵是否为二进制
        if not np.all((self.H == 0) | (self.H == 1)):
            return False

        # 检查行列是否线性无关（简单检查）
        rank = np.linalg.matrix_rank(self.H)
        if rank < self.m:
            warnings.warn(f"校验矩阵的秩({rank})小于行数({self.m})")

        return True

    def _generate_generator_matrix(self):
        """
        生成生成矩阵G

        对于系统码，G = [I_k | P]，其中I_k是k×k单位矩阵，P是校验部分
        """
        if not self.systematic:
            # 非系统码的生成矩阵生成（简化实现）
            return np.eye(self.k, self.n, dtype=int)

        # 对于系统LDPC码，使用简化方法
        # 直接从校验矩阵构造生成矩阵
        I_k = np.eye(self.k, dtype=int)

        # 简单方法：假设前k列对应信息位，后m列对应校验位
        # 对于手工构造的小型LDPC码，直接使用校验矩阵的前k列作为P^T
        if self.H.shape[1] >= self.k:
            P_T = self.H[:, :self.k]
            P = P_T.T % 2
        else:
            # 如果列数不够，使用零矩阵
            P = np.zeros((self.k, self.m), dtype=int)

        G = np.hstack([I_k, P])
        return G



    def encode(self, info_bits):
        """
        编码信息位

        参数:
            info_bits: 信息位序列，长度为k的numpy数组或列表

        返回:
            codeword: 编码后的码字，长度为n的numpy数组
        """
        info_bits = np.array(info_bits, dtype=int)

        if len(info_bits) != self.k:
            raise ValueError(f"信息位长度({len(info_bits)})与编码器参数不匹配(k={self.k})")

        # 编码：c = u * G (mod 2)
        codeword = np.dot(info_bits, self.G) % 2

        return codeword

    def get_code_parameters(self):
        """
        获取码的参数

        返回:
            dict: 包含码长n、信息位数k、校验位数m和码率的字典
        """
        return {
            'n': self.n,      # 码长
            'k': self.k,      # 信息位数
            'm': self.m,      # 校验位数
            'rate': self.k / self.n  # 码率
        }


class LDPCDecoder:
    """
    LDPC解码器类

    实现基于置信传播算法(Belief Propagation)的软判决解码
    """

    def __init__(self, H, max_iterations=50, convergence_threshold=1e-6):
        """
        初始化LDPC解码器

        参数:
            H: 校验矩阵 (m x n)
            max_iterations: 最大迭代次数
            convergence_threshold: 收敛阈值
        """
        self.H = np.array(H, dtype=int)
        self.m, self.n = self.H.shape
        self.max_iterations = max_iterations
        self.convergence_threshold = convergence_threshold

        # 预计算校验矩阵的连接关系
        self._precompute_connections()

    def _precompute_connections(self):
        """预计算变量节点和校验节点的连接关系"""
        # 变量节点到校验节点的连接
        self.var_to_check = []
        for j in range(self.n):
            self.var_to_check.append(np.where(self.H[:, j] == 1)[0])

        # 校验节点到变量节点的连接
        self.check_to_var = []
        for i in range(self.m):
            self.check_to_var.append(np.where(self.H[i, :] == 1)[0])

    def decode(self, received_llr, verbose=False):
        """
        解码接收到的对数似然比

        参数:
            received_llr: 接收到的对数似然比，长度为n的numpy数组
            verbose: 是否输出详细信息

        返回:
            decoded_bits: 解码后的比特序列
            converged: 是否收敛
            iterations: 实际迭代次数
        """
        received_llr = np.array(received_llr, dtype=float)

        if len(received_llr) != self.n:
            raise ValueError(f"接收LLR长度({len(received_llr)})与解码器参数不匹配(n={self.n})")

        # 初始化消息
        # var_to_check_msg[j][i] 表示变量节点j发送给校验节点i的消息
        var_to_check_msg = {}
        for j in range(self.n):
            var_to_check_msg[j] = {}
            for i in self.var_to_check[j]:
                var_to_check_msg[j][i] = received_llr[j]

        # check_to_var_msg[i][j] 表示校验节点i发送给变量节点j的消息
        check_to_var_msg = {}
        for i in range(self.m):
            check_to_var_msg[i] = {}
            for j in self.check_to_var[i]:
                check_to_var_msg[i][j] = 0.0

        # 迭代解码
        for iteration in range(self.max_iterations):
            # 保存上一次的消息用于收敛检查
            prev_var_msg = {j: dict(var_to_check_msg[j]) for j in range(self.n)}

            # 校验节点更新
            for i in range(self.m):
                connected_vars = self.check_to_var[i]
                for j in connected_vars:
                    # 计算除了变量j之外的所有变量的乘积
                    product = 1.0
                    for k in connected_vars:
                        if k != j:
                            product *= np.tanh(var_to_check_msg[k][i] / 2.0)

                    # 避免数值问题
                    product = np.clip(product, -0.999999, 0.999999)
                    check_to_var_msg[i][j] = 2.0 * np.arctanh(product)

            # 变量节点更新
            for j in range(self.n):
                connected_checks = self.var_to_check[j]
                for i in connected_checks:
                    # 计算除了校验i之外的所有校验消息的和
                    sum_msg = received_llr[j]
                    for k in connected_checks:
                        if k != i:
                            sum_msg += check_to_var_msg[k][j]

                    var_to_check_msg[j][i] = sum_msg

            # 检查收敛性
            converged = True
            for j in range(self.n):
                for i in self.var_to_check[j]:
                    if abs(var_to_check_msg[j][i] - prev_var_msg[j][i]) > self.convergence_threshold:
                        converged = False
                        break
                if not converged:
                    break

            if verbose and (iteration + 1) % 10 == 0:
                print(f"迭代 {iteration + 1}/{self.max_iterations}")

            if converged:
                if verbose:
                    print(f"在第 {iteration + 1} 次迭代后收敛")
                break

        # 计算最终的后验LLR并做硬判决
        posterior_llr = np.zeros(self.n)
        for j in range(self.n):
            posterior_llr[j] = received_llr[j]
            for i in self.var_to_check[j]:
                posterior_llr[j] += check_to_var_msg[i][j]

        # 硬判决：LLR > 0 表示比特为0，LLR < 0 表示比特为1
        decoded_bits = (posterior_llr < 0).astype(int)

        return decoded_bits, converged, iteration + 1

    def check_syndrome(self, codeword):
        """
        检查码字的校验子

        参数:
            codeword: 码字

        返回:
            syndrome: 校验子，全零表示无错误
        """
        codeword = np.array(codeword, dtype=int)
        syndrome = np.dot(self.H, codeword) % 2
        return syndrome

    def is_valid_codeword(self, codeword):
        """
        检查是否为有效码字

        参数:
            codeword: 待检查的码字

        返回:
            bool: 是否为有效码字
        """
        syndrome = self.check_syndrome(codeword)
        return np.all(syndrome == 0)


def generate_regular_ldpc_matrix(n, k, dv, dc):
    """
    生成规则LDPC校验矩阵

    参数:
        n: 码长
        k: 信息位数
        dv: 变量节点度数
        dc: 校验节点度数

    返回:
        H: 校验矩阵
    """
    m = n - k  # 校验位数

    # 检查参数一致性
    if n * dv != m * dc:
        raise ValueError("参数不一致：n*dv 必须等于 m*dc")

    # 创建度数序列
    var_degrees = [dv] * n
    check_degrees = [dc] * m

    # 使用边交换算法生成矩阵
    H = np.zeros((m, n), dtype=int)

    # 创建边列表
    edges = []
    var_id = 0
    for degree in var_degrees:
        for _ in range(degree):
            edges.append(var_id)
        var_id += 1

    # 随机打乱边
    np.random.shuffle(edges)

    # 分配边到校验节点
    edge_idx = 0
    for check_id in range(m):
        for _ in range(check_degrees[check_id]):
            if edge_idx < len(edges):
                var_id = edges[edge_idx]
                H[check_id, var_id] = 1
                edge_idx += 1

    return H


def generate_irregular_ldpc_matrix(n, k, var_degree_dist, check_degree_dist):
    """
    生成不规则LDPC校验矩阵

    参数:
        n: 码长
        k: 信息位数
        var_degree_dist: 变量节点度数分布 {度数: 节点数}
        check_degree_dist: 校验节点度数分布 {度数: 节点数}

    返回:
        H: 校验矩阵
    """
    m = n - k

    # 验证度数分布
    if sum(var_degree_dist.values()) != n:
        raise ValueError("变量节点度数分布的总数必须等于n")
    if sum(check_degree_dist.values()) != m:
        raise ValueError("校验节点度数分布的总数必须等于m")

    # 检查边数一致性
    total_var_edges = sum(degree * count for degree, count in var_degree_dist.items())
    total_check_edges = sum(degree * count for degree, count in check_degree_dist.items())

    if total_var_edges != total_check_edges:
        raise ValueError("变量节点和校验节点的总边数必须相等")

    H = np.zeros((m, n), dtype=int)

    # 创建变量节点度数序列
    var_degrees = []
    for degree, count in var_degree_dist.items():
        var_degrees.extend([degree] * count)

    # 创建校验节点度数序列
    check_degrees = []
    for degree, count in check_degree_dist.items():
        check_degrees.extend([degree] * count)

    # 创建边列表
    var_edges = []
    for var_id, degree in enumerate(var_degrees):
        var_edges.extend([var_id] * degree)

    check_edges = []
    for check_id, degree in enumerate(check_degrees):
        check_edges.extend([check_id] * degree)

    # 随机打乱
    np.random.shuffle(var_edges)
    np.random.shuffle(check_edges)

    # 连接边
    for var_id, check_id in zip(var_edges, check_edges):
        H[check_id, var_id] = 1

    return H


def awgn_channel(signal, snr_db):
    """
    AWGN信道模拟

    参数:
        signal: 输入信号 (BPSK: +1/-1)
        snr_db: 信噪比(dB)

    返回:
        received_signal: 接收信号
        noise_variance: 噪声方差
    """
    signal = np.array(signal, dtype=float)

    # 计算噪声方差
    snr_linear = 10 ** (snr_db / 10)
    signal_power = np.mean(signal ** 2)
    noise_variance = signal_power / snr_linear

    # 添加高斯噪声
    noise = np.random.normal(0, np.sqrt(noise_variance), len(signal))
    received_signal = signal + noise

    return received_signal, noise_variance


def bits_to_bpsk(bits):
    """
    将比特转换为BPSK符号

    参数:
        bits: 比特序列 (0/1)

    返回:
        symbols: BPSK符号 (+1/-1)
    """
    bits = np.array(bits, dtype=int)
    return 2 * bits - 1  # 0 -> -1, 1 -> +1


def bpsk_to_llr(received_signal, noise_variance):
    """
    将BPSK接收信号转换为对数似然比

    参数:
        received_signal: 接收信号 (BPSK: 0->-1, 1->+1)
        noise_variance: 噪声方差

    返回:
        llr: 对数似然比 (LLR > 0 表示比特为0，LLR < 0 表示比特为1)
    """
    received_signal = np.array(received_signal, dtype=float)
    # 对于BPSK: 0->-1, 1->+1
    # LLR = -2 * received_signal / noise_variance
    # 这样：接收到-1时LLR>0(倾向于比特0)，接收到+1时LLR<0(倾向于比特1)
    llr = -2 * received_signal / noise_variance
    return llr


def calculate_ber(original_bits, decoded_bits):
    """
    计算误码率

    参数:
        original_bits: 原始比特
        decoded_bits: 解码比特

    返回:
        ber: 误码率
    """
    original_bits = np.array(original_bits, dtype=int)
    decoded_bits = np.array(decoded_bits, dtype=int)

    if len(original_bits) != len(decoded_bits):
        raise ValueError("比特序列长度不匹配")

    errors = np.sum(original_bits != decoded_bits)
    ber = errors / len(original_bits)

    return ber


def ldpc_simulation(H, snr_range, num_frames=100, frame_length=None, verbose=True):
    """
    LDPC编解码性能仿真

    参数:
        H: 校验矩阵
        snr_range: 信噪比范围 (dB)
        num_frames: 仿真帧数
        frame_length: 每帧长度，如果为None则使用信息位长度
        verbose: 是否显示进度

    返回:
        snr_db_list: 信噪比列表
        ber_list: 误码率列表
        fer_list: 误帧率列表
    """
    # 创建编解码器
    encoder = LDPCEncoder(H)
    decoder = LDPCDecoder(H)

    code_params = encoder.get_code_parameters()
    k = code_params['k']

    if frame_length is None:
        frame_length = k

    snr_db_list = []
    ber_list = []
    fer_list = []

    for snr_db in snr_range:
        if verbose:
            print(f"\n仿真 SNR = {snr_db} dB")

        total_bits = 0
        total_errors = 0
        frame_errors = 0

        for frame in range(num_frames):
            if verbose and (frame + 1) % 20 == 0:
                print(f"  帧 {frame + 1}/{num_frames}")

            # 生成随机信息位
            info_bits = np.random.randint(0, 2, frame_length)

            # 编码
            codeword = encoder.encode(info_bits)

            # BPSK调制
            symbols = bits_to_bpsk(codeword)

            # 通过AWGN信道
            received_signal, noise_variance = awgn_channel(symbols, snr_db)

            # 计算LLR
            llr = bpsk_to_llr(received_signal, noise_variance)

            # 解码
            decoded_bits, converged, iterations = decoder.decode(llr)

            # 提取信息位（对于系统码）
            if encoder.systematic:
                decoded_info = decoded_bits[:frame_length]
            else:
                # 对于非系统码，需要更复杂的信息位提取
                decoded_info = decoded_bits[:frame_length]

            # 计算错误
            bit_errors = np.sum(info_bits != decoded_info)
            total_bits += frame_length
            total_errors += bit_errors

            if bit_errors > 0:
                frame_errors += 1

        # 计算误码率和误帧率
        ber = total_errors / total_bits if total_bits > 0 else 0
        fer = frame_errors / num_frames

        snr_db_list.append(snr_db)
        ber_list.append(ber)
        fer_list.append(fer)

        if verbose:
            print(f"  BER = {ber:.2e}, FER = {fer:.2e}")

    return snr_db_list, ber_list, fer_list


def plot_performance(snr_db_list, ber_list, fer_list=None, title="LDPC Performance"):
    """
    绘制LDPC性能曲线

    参数:
        snr_db_list: 信噪比列表
        ber_list: 误码率列表
        fer_list: 误帧率列表（可选）
        title: 图标题
    """
    try:
        import matplotlib.pyplot as plt

        plt.figure(figsize=(10, 6))

        # 绘制BER曲线
        plt.semilogy(snr_db_list, ber_list, 'b-o', label='BER', linewidth=2, markersize=6)

        # 绘制FER曲线（如果提供）
        if fer_list is not None:
            plt.semilogy(snr_db_list, fer_list, 'r-s', label='FER', linewidth=2, markersize=6)

        plt.xlabel('SNR (dB)')
        plt.ylabel('Error Rate')
        plt.title(title)
        plt.grid(True, alpha=0.3)
        plt.legend()
        plt.xlim(min(snr_db_list), max(snr_db_list))
        plt.ylim(1e-6, 1)

        plt.tight_layout()
        plt.show()

    except ImportError:
        print("matplotlib未安装，无法绘制图形")
        print("SNR (dB) | BER | FER")
        print("-" * 25)
        for i, snr in enumerate(snr_db_list):
            if fer_list is not None:
                print(f"{snr:8.1f} | {ber_list[i]:.2e} | {fer_list[i]:.2e}")
            else:
                print(f"{snr:8.1f} | {ber_list[i]:.2e}")


def create_example_ldpc_codes():
    """
    创建一些示例LDPC码

    返回:
        dict: 包含不同LDPC码的字典
    """
    examples = {}

    # 示例1：小型规则LDPC码 (15,11)
    try:
        H1 = generate_regular_ldpc_matrix(n=15, k=11, dv=3, dc=11)
        examples['regular_15_11'] = {
            'H': H1,
            'description': '规则LDPC码 (15,11), dv=3, dc=11'
        }
    except:
        pass

    # 示例2：中等规则LDPC码 (63,45)
    try:
        H2 = generate_regular_ldpc_matrix(n=63, k=45, dv=3, dc=10)
        examples['regular_63_45'] = {
            'H': H2,
            'description': '规则LDPC码 (63,45), dv=3, dc=10'
        }
    except:
        pass

    # 示例3：手工构造的小型LDPC码
    H3 = np.array([
        [1, 1, 0, 1, 0, 0],
        [0, 1, 1, 0, 1, 0],
        [1, 0, 1, 0, 0, 1]
    ])
    examples['manual_6_3'] = {
        'H': H3,
        'description': '手工构造LDPC码 (6,3)'
    }

    # 示例4：不规则LDPC码
    try:
        var_dist = {2: 10, 3: 20}  # 10个度数为2的节点，20个度数为3的节点
        check_dist = {5: 12}       # 12个度数为5的节点
        H4 = generate_irregular_ldpc_matrix(n=30, k=18,
                                          var_degree_dist=var_dist,
                                          check_degree_dist=check_dist)
        examples['irregular_30_18'] = {
            'H': H4,
            'description': '不规则LDPC码 (30,18)'
        }
    except:
        pass

    return examples


def test_ldpc_basic():
    """
    基本LDPC编解码测试
    """
    print("=== LDPC编解码基本测试 ===\n")

    # 使用手工构造的小型LDPC码进行测试
    H = np.array([
        [1, 1, 0, 1, 0, 0],
        [0, 1, 1, 0, 1, 0],
        [1, 0, 1, 0, 0, 1]
    ])

    print("校验矩阵 H:")
    print(H)
    print()

    # 创建编解码器
    encoder = LDPCEncoder(H)
    decoder = LDPCDecoder(H)

    # 显示码参数
    params = encoder.get_code_parameters()
    print(f"码参数: n={params['n']}, k={params['k']}, m={params['m']}, 码率={params['rate']:.3f}")
    print()

    # 测试编码
    info_bits = np.array([1, 0, 1])
    print(f"信息位: {info_bits}")

    codeword = encoder.encode(info_bits)
    print(f"编码后: {codeword}")

    # 验证码字
    is_valid = decoder.is_valid_codeword(codeword)
    print(f"码字有效性: {is_valid}")
    print()

    # 测试无噪声解码
    symbols = bits_to_bpsk(codeword)
    llr = bpsk_to_llr(symbols, 0.01)  # 很小的噪声方差

    decoded_bits, converged, iterations = decoder.decode(llr, verbose=True)
    print(f"解码结果: {decoded_bits}")
    print(f"收敛状态: {converged}, 迭代次数: {iterations}")

    # 提取信息位
    decoded_info = decoded_bits[:params['k']]
    print(f"解码信息位: {decoded_info}")

    # 计算误码率
    ber = calculate_ber(info_bits, decoded_info)
    print(f"误码率: {ber}")
    print()

    # 测试有噪声情况
    print("=== 有噪声测试 (SNR = 2 dB) ===")
    received_signal, noise_var = awgn_channel(symbols, snr_db=2)
    llr_noisy = bpsk_to_llr(received_signal, noise_var)

    decoded_bits_noisy, converged_noisy, iter_noisy = decoder.decode(llr_noisy, verbose=True)
    decoded_info_noisy = decoded_bits_noisy[:params['k']]

    print(f"噪声下解码信息位: {decoded_info_noisy}")
    ber_noisy = calculate_ber(info_bits, decoded_info_noisy)
    print(f"噪声下误码率: {ber_noisy}")


# ==================== SC-LDPC (Spatially Coupled LDPC) 实现 ====================

class SCLDPCConstructor:
    """
    SC-LDPC码构造器

    实现基于原型矩阵的空间耦合LDPC码构造
    """

    def __init__(self, base_matrix, coupling_length, memory_length=1):
        """
        初始化SC-LDPC构造器

        参数:
            base_matrix: 基础原型矩阵 (m_b x n_b)
            coupling_length: 耦合长度 L
            memory_length: 记忆长度 m_s (默认为1)
        """
        self.base_matrix = np.array(base_matrix, dtype=int)
        self.m_b, self.n_b = self.base_matrix.shape  # 基础矩阵维度
        self.L = coupling_length  # 耦合长度
        self.m_s = memory_length  # 记忆长度

        # 验证参数
        if self.L <= 0:
            raise ValueError("耦合长度必须大于0")
        if self.m_s <= 0:
            raise ValueError("记忆长度必须大于0")

        # 计算SC-LDPC码参数
        self.m_total = self.m_b * (self.L + self.m_s - 1)  # 总校验节点数
        self.n_total = self.n_b * self.L  # 总变量节点数

    def construct_coupled_matrix(self, termination='zero_padding'):
        """
        构造空间耦合校验矩阵

        参数:
            termination: 终止方式 ('zero_padding', 'tail_biting')

        返回:
            H_coupled: 耦合后的校验矩阵
        """
        # 初始化耦合矩阵
        H_coupled = np.zeros((self.m_total, self.n_total), dtype=int)

        # 构造耦合结构
        for i in range(self.L):
            # 对于每个位置i，添加记忆长度范围内的连接
            for j in range(self.m_s):
                # 计算校验节点和变量节点的位置
                check_start = (i + j) * self.m_b
                check_end = check_start + self.m_b
                var_start = i * self.n_b
                var_end = var_start + self.n_b

                # 检查边界条件
                if check_end <= self.m_total and var_end <= self.n_total:
                    # 在相应位置放置基础矩阵
                    H_coupled[check_start:check_end, var_start:var_end] = self.base_matrix

        return H_coupled

    def construct_protograph_matrix(self, protograph_edges):
        """
        基于原图构造SC-LDPC矩阵

        参数:
            protograph_edges: 原图边列表 [(var_node, check_node, weight), ...]

        返回:
            H_coupled: 耦合后的校验矩阵
        """
        # 初始化耦合矩阵
        H_coupled = np.zeros((self.m_total, self.n_total), dtype=int)

        # 根据原图边构造耦合结构
        for var_node, check_node, weight in protograph_edges:
            for i in range(self.L):
                for j in range(self.m_s):
                    # 计算实际的节点位置
                    actual_check = (i + j) * self.m_b + check_node
                    actual_var = i * self.n_b + var_node

                    # 检查边界
                    if actual_check < self.m_total and actual_var < self.n_total:
                        H_coupled[actual_check, actual_var] = weight

        return H_coupled

    def get_code_parameters(self):
        """
        获取SC-LDPC码参数

        返回:
            dict: 码参数字典
        """
        k_total = self.n_total - self.m_total  # 近似信息位数
        rate = k_total / self.n_total if self.n_total > 0 else 0

        return {
            'n_total': self.n_total,
            'k_total': k_total,
            'm_total': self.m_total,
            'rate': rate,
            'coupling_length': self.L,
            'memory_length': self.m_s,
            'base_matrix_size': (self.m_b, self.n_b)
        }


class SCLDPCEncoder:
    """
    SC-LDPC编码器

    实现基于空间耦合结构的LDPC编码
    """

    def __init__(self, H_coupled, systematic=True):
        """
        初始化SC-LDPC编码器

        参数:
            H_coupled: 空间耦合校验矩阵
            systematic: 是否使用系统编码
        """
        self.H = np.array(H_coupled, dtype=int)
        self.m, self.n = self.H.shape
        self.k = self.n - self.m  # 近似信息位数
        self.systematic = systematic

        if self.k <= 0:
            # 对于SC-LDPC，有时会出现k<=0的情况，我们调整为最小值1
            self.k = max(1, min(self.n // 2, self.n - self.m + 1))
            if self.k <= 0:
                self.k = 1

        # 生成生成矩阵（简化实现）
        self.G = self._generate_generator_matrix()

    def _generate_generator_matrix(self):
        """
        生成SC-LDPC的生成矩阵

        对于SC-LDPC码，由于其特殊结构，使用简化的生成矩阵构造方法
        """
        if self.k <= 0:
            # 如果信息位数为0或负数，返回空矩阵
            return np.zeros((max(1, self.k), self.n), dtype=int)

        if not self.systematic:
            return np.eye(self.k, self.n, dtype=int)

        # 对于系统SC-LDPC码，使用简化方法
        I_k = np.eye(self.k, dtype=int)

        # 构造校验部分（简化实现）
        if self.H.shape[1] >= self.k and self.m > 0:
            # 使用校验矩阵的前k列构造校验部分
            P_T = self.H[:, :self.k]
            P = P_T.T % 2
        else:
            P = np.zeros((self.k, self.m), dtype=int)

        G = np.hstack([I_k, P])
        return G

    def encode(self, info_bits):
        """
        编码信息位

        参数:
            info_bits: 信息位序列

        返回:
            codeword: 编码后的码字
        """
        info_bits = np.array(info_bits, dtype=int)

        if len(info_bits) != self.k:
            raise ValueError(f"信息位长度({len(info_bits)})与编码器参数不匹配(k={self.k})")

        # 编码：c = u * G (mod 2)
        codeword = np.dot(info_bits, self.G) % 2
        return codeword

    def encode_streaming(self, info_stream, block_size):
        """
        流式编码（适用于长SC-LDPC码）

        参数:
            info_stream: 信息位流
            block_size: 每次处理的块大小

        返回:
            codeword_stream: 编码后的码字流
        """
        info_stream = np.array(info_stream, dtype=int)
        codeword_stream = []

        # 分块处理
        for i in range(0, len(info_stream), block_size):
            block = info_stream[i:i+block_size]

            # 如果块大小不足，进行填充
            if len(block) < block_size:
                block = np.pad(block, (0, block_size - len(block)), 'constant')

            # 编码当前块
            if len(block) <= self.k:
                # 填充到k长度
                padded_block = np.pad(block, (0, self.k - len(block)), 'constant')
                encoded_block = self.encode(padded_block)
                codeword_stream.extend(encoded_block)

        return np.array(codeword_stream, dtype=int)

    def get_code_parameters(self):
        """
        获取编码器参数

        返回:
            dict: 参数字典
        """
        return {
            'n': self.n,
            'k': self.k,
            'm': self.m,
            'rate': self.k / self.n,
            'systematic': self.systematic
        }


class SCLDPCDecoder:
    """
    SC-LDPC解码器

    实现基于置信传播算法的SC-LDPC硬解码和软解码
    """

    def __init__(self, H_coupled, max_iterations=50, convergence_threshold=1e-6):
        """
        初始化SC-LDPC解码器

        参数:
            H_coupled: 空间耦合校验矩阵
            max_iterations: 最大迭代次数
            convergence_threshold: 收敛阈值
        """
        self.H = np.array(H_coupled, dtype=int)
        self.m, self.n = self.H.shape
        self.max_iterations = max_iterations
        self.convergence_threshold = convergence_threshold

        # 预计算连接关系
        self._precompute_connections()

    def _precompute_connections(self):
        """预计算变量节点和校验节点的连接关系"""
        # 变量节点到校验节点的连接
        self.var_to_check = []
        for j in range(self.n):
            self.var_to_check.append(np.where(self.H[:, j] == 1)[0])

        # 校验节点到变量节点的连接
        self.check_to_var = []
        for i in range(self.m):
            self.check_to_var.append(np.where(self.H[i, :] == 1)[0])

    def decode_hard(self, received_bits, verbose=False):
        """
        硬判决解码

        参数:
            received_bits: 接收到的硬判决比特
            verbose: 是否输出详细信息

        返回:
            decoded_bits: 解码后的比特序列
            converged: 是否收敛
            iterations: 实际迭代次数
        """
        received_bits = np.array(received_bits, dtype=int)

        if len(received_bits) != self.n:
            raise ValueError(f"接收比特长度({len(received_bits)})与解码器参数不匹配(n={self.n})")

        # 初始化
        decoded_bits = received_bits.copy()

        for iteration in range(self.max_iterations):
            prev_bits = decoded_bits.copy()

            # 计算校验子
            syndrome = np.dot(self.H, decoded_bits) % 2

            # 如果校验子全为0，则解码成功
            if np.all(syndrome == 0):
                if verbose:
                    print(f"硬解码在第 {iteration + 1} 次迭代后收敛")
                return decoded_bits, True, iteration + 1

            # 对每个变量节点进行翻转判决
            for j in range(self.n):
                # 计算与该变量节点相连的不满足校验方程的数量
                connected_checks = self.var_to_check[j]
                unsatisfied_count = np.sum(syndrome[connected_checks])

                # 如果大多数校验方程不满足，则翻转该比特
                if unsatisfied_count > len(connected_checks) / 2:
                    decoded_bits[j] = 1 - decoded_bits[j]

            # 检查是否有变化
            if np.array_equal(decoded_bits, prev_bits):
                if verbose:
                    print(f"硬解码在第 {iteration + 1} 次迭代后停止（无变化）")
                break

            if verbose and (iteration + 1) % 10 == 0:
                print(f"硬解码迭代 {iteration + 1}/{self.max_iterations}")

        return decoded_bits, False, iteration + 1

    def decode_soft(self, received_llr, verbose=False):
        """
        软判决解码（基于置信传播算法）

        参数:
            received_llr: 接收到的对数似然比
            verbose: 是否输出详细信息

        返回:
            decoded_bits: 解码后的比特序列
            converged: 是否收敛
            iterations: 实际迭代次数
        """
        received_llr = np.array(received_llr, dtype=float)

        if len(received_llr) != self.n:
            raise ValueError(f"接收LLR长度({len(received_llr)})与解码器参数不匹配(n={self.n})")

        # 初始化消息
        var_to_check_msg = {}
        for j in range(self.n):
            var_to_check_msg[j] = {}
            for i in self.var_to_check[j]:
                var_to_check_msg[j][i] = received_llr[j]

        check_to_var_msg = {}
        for i in range(self.m):
            check_to_var_msg[i] = {}
            for j in self.check_to_var[i]:
                check_to_var_msg[i][j] = 0.0

        # 迭代解码
        for iteration in range(self.max_iterations):
            # 保存上一次的消息用于收敛检查
            prev_var_msg = {j: dict(var_to_check_msg[j]) for j in range(self.n)}

            # 校验节点更新
            for i in range(self.m):
                connected_vars = self.check_to_var[i]
                for j in connected_vars:
                    # 计算除了变量j之外的所有变量的乘积
                    product = 1.0
                    for k in connected_vars:
                        if k != j:
                            product *= np.tanh(var_to_check_msg[k][i] / 2.0)

                    # 避免数值问题
                    product = np.clip(product, -0.999999, 0.999999)
                    check_to_var_msg[i][j] = 2.0 * np.arctanh(product)

            # 变量节点更新
            for j in range(self.n):
                connected_checks = self.var_to_check[j]
                for i in connected_checks:
                    # 计算除了校验i之外的所有校验消息的和
                    sum_msg = received_llr[j]
                    for k in connected_checks:
                        if k != i:
                            sum_msg += check_to_var_msg[k][j]

                    var_to_check_msg[j][i] = sum_msg

            # 检查收敛性
            converged = True
            for j in range(self.n):
                for i in self.var_to_check[j]:
                    if abs(var_to_check_msg[j][i] - prev_var_msg[j][i]) > self.convergence_threshold:
                        converged = False
                        break
                if not converged:
                    break

            if verbose and (iteration + 1) % 10 == 0:
                print(f"软解码迭代 {iteration + 1}/{self.max_iterations}")

            if converged:
                if verbose:
                    print(f"软解码在第 {iteration + 1} 次迭代后收敛")
                break

        # 计算最终的后验LLR并做硬判决
        posterior_llr = np.zeros(self.n)
        for j in range(self.n):
            posterior_llr[j] = received_llr[j]
            for i in self.var_to_check[j]:
                posterior_llr[j] += check_to_var_msg[i][j]

        # 硬判决：LLR > 0 表示比特为0，LLR < 0 表示比特为1
        decoded_bits = (posterior_llr < 0).astype(int)

        return decoded_bits, converged, iteration + 1

    def decode_sliding_window(self, received_llr, window_size, overlap=0, verbose=False):
        """
        滑窗解码（适用于长SC-LDPC码）

        参数:
            received_llr: 接收到的对数似然比
            window_size: 滑窗大小
            overlap: 窗口重叠大小
            verbose: 是否输出详细信息

        返回:
            decoded_bits: 解码后的比特序列
            avg_iterations: 平均迭代次数
        """
        received_llr = np.array(received_llr, dtype=float)

        if len(received_llr) != self.n:
            raise ValueError(f"接收LLR长度({len(received_llr)})与解码器参数不匹配(n={self.n})")

        decoded_bits = np.zeros(self.n, dtype=int)
        total_iterations = 0
        num_windows = 0

        # 计算步长
        step_size = window_size - overlap

        for start in range(0, self.n, step_size):
            end = min(start + window_size, self.n)

            if end - start < window_size // 2:  # 如果窗口太小，跳过
                break

            # 提取当前窗口的LLR
            window_llr = received_llr[start:end]

            # 提取对应的校验矩阵子块
            # 这里简化处理，实际应该根据SC-LDPC的结构来提取
            window_H = self.H[:, start:end]

            # 创建临时解码器
            if np.any(window_H):  # 确保子矩阵不为空
                temp_decoder = SCLDPCDecoder(window_H, self.max_iterations, self.convergence_threshold)
                window_decoded, converged, iterations = temp_decoder.decode_soft(window_llr, verbose=False)

                # 处理重叠区域（简单平均）
                if overlap > 0 and start > 0:
                    overlap_start = max(0, start)
                    overlap_end = min(start + overlap, end)
                    # 在重叠区域使用软判决结合
                    for i in range(overlap_start, overlap_end):
                        if i - start < len(window_decoded):
                            # 简单的硬判决结合
                            decoded_bits[i] = window_decoded[i - start]
                else:
                    # 非重叠区域直接复制
                    copy_end = min(end, start + len(window_decoded))
                    decoded_bits[start:copy_end] = window_decoded[:copy_end-start]

                total_iterations += iterations
                num_windows += 1

                if verbose:
                    print(f"窗口 [{start}:{end}] 解码完成，迭代次数: {iterations}")

        avg_iterations = total_iterations / num_windows if num_windows > 0 else 0

        return decoded_bits, avg_iterations

    def check_syndrome(self, codeword):
        """
        检查码字的校验子

        参数:
            codeword: 码字

        返回:
            syndrome: 校验子，全零表示无错误
        """
        codeword = np.array(codeword, dtype=int)
        syndrome = np.dot(self.H, codeword) % 2
        return syndrome

    def is_valid_codeword(self, codeword):
        """
        检查是否为有效码字

        参数:
            codeword: 待检查的码字

        返回:
            bool: 是否为有效码字
        """
        syndrome = self.check_syndrome(codeword)
        return np.all(syndrome == 0)


def generate_sc_ldpc_matrix(base_matrix, coupling_length, memory_length=1):
    """
    生成SC-LDPC校验矩阵的便捷函数

    参数:
        base_matrix: 基础原型矩阵
        coupling_length: 耦合长度
        memory_length: 记忆长度

    返回:
        H_coupled: 空间耦合校验矩阵
    """
    constructor = SCLDPCConstructor(base_matrix, coupling_length, memory_length)
    return constructor.construct_coupled_matrix()


def create_example_sc_ldpc_codes():
    """
    创建一些示例SC-LDPC码

    返回:
        dict: 包含不同SC-LDPC码的字典
    """
    examples = {}

    # 示例1：基于简单原型矩阵的SC-LDPC码
    base_matrix_1 = np.array([
        [1, 1, 0],
        [0, 1, 1]
    ])

    try:
        H_sc_1 = generate_sc_ldpc_matrix(base_matrix_1, coupling_length=5, memory_length=2)
        examples['sc_ldpc_5_2'] = {
            'H': H_sc_1,
            'base_matrix': base_matrix_1,
            'description': 'SC-LDPC码，耦合长度=5，记忆长度=2'
        }
    except Exception as e:
        print(f"创建SC-LDPC示例1失败: {e}")

    # 示例2：基于(3,6)规则LDPC的SC-LDPC码
    base_matrix_2 = np.array([
        [1, 1, 1, 0, 0, 0],
        [0, 0, 1, 1, 1, 0],
        [1, 0, 0, 0, 1, 1]
    ])

    try:
        H_sc_2 = generate_sc_ldpc_matrix(base_matrix_2, coupling_length=4, memory_length=1)
        examples['sc_ldpc_4_1'] = {
            'H': H_sc_2,
            'base_matrix': base_matrix_2,
            'description': 'SC-LDPC码，耦合长度=4，记忆长度=1'
        }
    except Exception as e:
        print(f"创建SC-LDPC示例2失败: {e}")

    # 示例3：小型SC-LDPC码用于测试
    base_matrix_3 = np.array([
        [1, 1],
        [1, 0]
    ])

    try:
        H_sc_3 = generate_sc_ldpc_matrix(base_matrix_3, coupling_length=3, memory_length=1)
        examples['sc_ldpc_small'] = {
            'H': H_sc_3,
            'base_matrix': base_matrix_3,
            'description': '小型SC-LDPC码，用于测试'
        }
    except Exception as e:
        print(f"创建SC-LDPC示例3失败: {e}")

    return examples


def sc_ldpc_simulation(H_coupled, snr_range, num_frames=100, decode_method='soft', verbose=True):
    """
    SC-LDPC编解码性能仿真

    参数:
        H_coupled: 空间耦合校验矩阵
        snr_range: 信噪比范围 (dB)
        num_frames: 仿真帧数
        decode_method: 解码方法 ('soft', 'hard', 'sliding_window')
        verbose: 是否显示进度

    返回:
        snr_db_list: 信噪比列表
        ber_list: 误码率列表
        fer_list: 误帧率列表
    """
    # 创建编解码器
    encoder = SCLDPCEncoder(H_coupled)
    decoder = SCLDPCDecoder(H_coupled)

    code_params = encoder.get_code_parameters()
    k = code_params['k']

    snr_db_list = []
    ber_list = []
    fer_list = []

    for snr_db in snr_range:
        if verbose:
            print(f"\n仿真 SNR = {snr_db} dB (解码方法: {decode_method})")

        total_bits = 0
        total_errors = 0
        frame_errors = 0
        total_iterations = 0

        for frame in range(num_frames):
            if verbose and (frame + 1) % 20 == 0:
                print(f"  帧 {frame + 1}/{num_frames}")

            # 生成随机信息位
            info_bits = np.random.randint(0, 2, k)

            # 编码
            codeword = encoder.encode(info_bits)

            # BPSK调制
            symbols = bits_to_bpsk(codeword)

            # 通过AWGN信道
            received_signal, noise_variance = awgn_channel(symbols, snr_db)

            # 解码
            if decode_method == 'soft':
                # 软解码
                llr = bpsk_to_llr(received_signal, noise_variance)
                decoded_bits, converged, iterations = decoder.decode_soft(llr)
            elif decode_method == 'hard':
                # 硬解码
                hard_bits = (received_signal < 0).astype(int)  # 硬判决
                decoded_bits, converged, iterations = decoder.decode_hard(hard_bits)
            elif decode_method == 'sliding_window':
                # 滑窗解码
                llr = bpsk_to_llr(received_signal, noise_variance)
                window_size = min(len(llr) // 2, 100)  # 自适应窗口大小
                decoded_bits, iterations = decoder.decode_sliding_window(llr, window_size, overlap=10)
                converged = True  # 滑窗解码总是"收敛"
            else:
                raise ValueError(f"未知的解码方法: {decode_method}")

            # 提取信息位（对于系统码）
            if encoder.systematic and k <= len(decoded_bits):
                decoded_info = decoded_bits[:k]
            else:
                decoded_info = decoded_bits[:k] if len(decoded_bits) >= k else np.pad(decoded_bits, (0, k - len(decoded_bits)), 'constant')

            # 计算错误
            bit_errors = np.sum(info_bits != decoded_info)
            total_bits += k
            total_errors += bit_errors
            total_iterations += iterations

            if bit_errors > 0:
                frame_errors += 1

        # 计算误码率和误帧率
        ber = total_errors / total_bits if total_bits > 0 else 0
        fer = frame_errors / num_frames
        avg_iterations = total_iterations / num_frames

        snr_db_list.append(snr_db)
        ber_list.append(ber)
        fer_list.append(fer)

        if verbose:
            print(f"  BER = {ber:.2e}, FER = {fer:.2e}, 平均迭代次数 = {avg_iterations:.1f}")

    return snr_db_list, ber_list, fer_list


def test_sc_ldpc_basic():
    """
    SC-LDPC编解码基本测试
    """
    print("=== SC-LDPC编解码基本测试 ===\n")

    # 创建示例SC-LDPC码
    examples = create_example_sc_ldpc_codes()

    if not examples:
        print("无法创建SC-LDPC示例码")
        return

    # 使用第一个可用的示例
    example_name = list(examples.keys())[0]
    example = examples[example_name]
    H_coupled = example['H']

    print(f"使用示例: {example['description']}")
    print(f"耦合矩阵形状: {H_coupled.shape}")
    print()

    # 创建编解码器
    encoder = SCLDPCEncoder(H_coupled)
    decoder = SCLDPCDecoder(H_coupled)

    # 显示码参数
    params = encoder.get_code_parameters()
    print(f"码参数: n={params['n']}, k={params['k']}, m={params['m']}, 码率={params['rate']:.3f}")
    print()

    # 测试编码
    k = params['k']
    if k > 0:
        info_bits = np.random.randint(0, 2, k)
        print(f"信息位 ({len(info_bits)}): {info_bits}")

        codeword = encoder.encode(info_bits)
        print(f"编码后 ({len(codeword)}): {codeword}")

        # 验证码字
        is_valid = decoder.is_valid_codeword(codeword)
        print(f"码字有效性: {is_valid}")
        print()

        # 测试软解码
        print("=== 软解码测试 ===")
        symbols = bits_to_bpsk(codeword)
        llr = bpsk_to_llr(symbols, 0.01)  # 很小的噪声方差

        decoded_bits, converged, iterations = decoder.decode_soft(llr, verbose=True)
        decoded_info = decoded_bits[:k] if len(decoded_bits) >= k else decoded_bits

        print(f"解码结果: {decoded_bits}")
        print(f"解码信息位: {decoded_info}")
        print(f"收敛状态: {converged}, 迭代次数: {iterations}")

        # 计算误码率
        if len(decoded_info) == len(info_bits):
            ber = calculate_ber(info_bits, decoded_info)
            print(f"误码率: {ber}")
        print()

        # 测试硬解码
        print("=== 硬解码测试 ===")
        hard_bits = (np.random.random(len(codeword)) > 0.1).astype(int)  # 添加一些错误
        decoded_hard, converged_hard, iter_hard = decoder.decode_hard(hard_bits, verbose=True)

        print(f"接收硬比特: {hard_bits}")
        print(f"硬解码结果: {decoded_hard}")
        print(f"收敛状态: {converged_hard}, 迭代次数: {iter_hard}")
        print()

        # 测试滑窗解码（如果码长足够大）
        if len(codeword) > 20:
            print("=== 滑窗解码测试 ===")
            window_size = min(len(codeword) // 2, 50)
            decoded_sliding, avg_iter = decoder.decode_sliding_window(llr, window_size, overlap=5, verbose=True)

            print(f"滑窗解码结果: {decoded_sliding}")
            print(f"平均迭代次数: {avg_iter:.1f}")
    else:
        print("警告：信息位数为0，无法进行编码测试")


def compare_sc_ldpc_methods():
    """
    比较不同SC-LDPC解码方法的性能
    """
    print("=== SC-LDPC解码方法性能比较 ===\n")

    # 创建示例SC-LDPC码
    examples = create_example_sc_ldpc_codes()

    if not examples:
        print("无法创建SC-LDPC示例码")
        return

    # 使用第一个可用的示例
    example_name = list(examples.keys())[0]
    example = examples[example_name]
    H_coupled = example['H']

    print(f"使用示例: {example['description']}")
    print(f"耦合矩阵形状: {H_coupled.shape}")
    print()

    # 测试参数
    snr_range = [0, 2, 4]
    num_frames = 50
    methods = ['soft', 'hard']

    # 如果码长足够大，添加滑窗解码
    if H_coupled.shape[1] > 50:
        methods.append('sliding_window')

    results = {}

    for method in methods:
        print(f"\n测试解码方法: {method}")
        try:
            snr_list, ber_list, fer_list = sc_ldpc_simulation(
                H_coupled, snr_range, num_frames, decode_method=method, verbose=True
            )
            results[method] = {
                'snr': snr_list,
                'ber': ber_list,
                'fer': fer_list
            }
        except Exception as e:
            print(f"方法 {method} 测试失败: {e}")

    # 显示比较结果
    print("\n=== 性能比较结果 ===")
    print("SNR(dB) | 方法 | BER | FER")
    print("-" * 40)

    for snr in snr_range:
        for method in results:
            if snr in results[method]['snr']:
                idx = results[method]['snr'].index(snr)
                ber = results[method]['ber'][idx]
                fer = results[method]['fer'][idx]
                print(f"{snr:7.1f} | {method:>12} | {ber:.2e} | {fer:.2e}")
        print("-" * 40)


if __name__ == "__main__":
    # 运行基本测试
    # test_ldpc_basic()

    H = np.array([
        [1, 1, 1, 0],
        [1, 0, 0, 1]
    ])

    H = generate_regular_ldpc_matrix(8, 4, 2, 4)
    print(H)
    encoder = LDPCEncoder(H)
    print(encoder.G)
    encoded = encoder.encode([0,1,1,1])
    print(encoded)
    decoder = LDPCDecoder(H)
    syndrome = decoder.check_syndrome(encoded)
    print(syndrome)
    ans = decoder.decode(-encoded * 2 + 1, True)
    print(ans)
